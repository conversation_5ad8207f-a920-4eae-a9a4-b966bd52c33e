import { isNil } from 'lodash-es';
import { openComplianceCheckExclusionDetailsModal } from '@components/compliance-check-exclusion-details-modal';
import {
    activeAssetController,
    COMPLIANCE_CHECK_STATUS,
    type DeviceValidComplianceCheckType,
} from '@controllers/asset';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Feedback } from '@cosmos/components/feedback';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer, runInAction } from '@globals/mobx';
import { AssetDetailsOpenAntivirusAgentModalButton } from './asset-details-open-antivirus-agent-modal-button';
import { buildComplianceCheckStatusDescription } from './helpers/buildComplianceCheckStatusDescription.helper';
import { buildComplianceCheckStatusMetadata } from './helpers/buildComplianceCheckStatusMetadata.helper';

interface AssetDetailsDeviceComplianceCheckProps {
    checkLabel: string;
    checkType: DeviceValidComplianceCheckType;
}

export const AssetDetailsDeviceComplianceCheckComponent = observer(
    ({
        checkLabel,
        checkType,
    }: AssetDetailsDeviceComplianceCheckProps): React.JSX.Element => {
        const {
            assetDeviceValidComplianceChecks,
            assetDeviceHasAntivirusEvidence,
            assetDeviceHasEdrAgent,
            edrProviderName,
            isEdrConnectionEnabled,
        } = activeAssetController;

        const complianceCheck = isNil(assetDeviceValidComplianceChecks)
            ? null
            : assetDeviceValidComplianceChecks[checkType];

        const { status, exclusion, lastCheckedAt } = complianceCheck ?? {};

        const {
            id: exclusionId,
            reason: exclusionReason,
            endDate: exclusionEndDate,
        } = exclusion ?? {};

        const isExcluded = status === COMPLIANCE_CHECK_STATUS.EXCLUDED;
        const isAntivirusCheck = checkType === 'ANTIVIRUS';

        const { label, iconName, colorScheme } =
            buildComplianceCheckStatusMetadata(status);

        const description = buildComplianceCheckStatusDescription(
            status,
            exclusionEndDate,
            lastCheckedAt,
        );

        return (
            <Stack
                direction="column"
                gap="1x"
                data-testid="AssetsDetailsDeviceComplianceCheckComponent"
                data-id="H3d98RcQ"
            >
                <KeyValuePair
                    type="REACT_NODE"
                    label={checkLabel}
                    value={
                        <Stack direction="column" gap="sm">
                            <Stack gap="md" align="center">
                                <Metadata
                                    type="status"
                                    label={label}
                                    iconName={iconName}
                                    colorScheme={colorScheme}
                                />
                                <Text size="100" colorScheme="faded">
                                    {description}
                                </Text>
                            </Stack>

                            {isExcluded && !isNil(exclusion) && (
                                <Stack
                                    direction="column"
                                    gap="sm"
                                    align="start"
                                >
                                    {!isNil(exclusionReason) && (
                                        <Text size="100">
                                            {exclusionReason}
                                        </Text>
                                    )}
                                    {!isNil(exclusionId) && (
                                        <Button
                                            size="sm"
                                            level="tertiary"
                                            colorScheme="primary"
                                            label="View exclusion"
                                            hasPadding={false}
                                            data-testid="AssetDetailsViewExclusionButton"
                                            data-id="view-exclusion-button"
                                            onClick={() => {
                                                runInAction(() => {
                                                    const { assetDetails } =
                                                        activeAssetController;
                                                    const owner =
                                                        assetDetails?.owner;

                                                    if (owner) {
                                                        openComplianceCheckExclusionDetailsModal(
                                                            exclusionId,
                                                            owner,
                                                        );
                                                    }
                                                });
                                            }}
                                        />
                                    )}
                                </Stack>
                            )}

                            {isAntivirusCheck && isEdrConnectionEnabled && (
                                <Stack direction="column" gap="sm">
                                    <Text size="100">
                                        {assetDeviceHasAntivirusEvidence
                                            ? `Evidence of antivirus was manually added on
                                                                the Personnel page.`
                                            : `This information is powered by your ${edrProviderName} connection`}
                                    </Text>

                                    {!assetDeviceHasEdrAgent &&
                                        !assetDeviceHasAntivirusEvidence && (
                                            <Feedback
                                                severity="warning"
                                                title={`${edrProviderName} agent not found`}
                                            />
                                        )}

                                    {assetDeviceHasEdrAgent &&
                                        !assetDeviceHasAntivirusEvidence && (
                                            <Box>
                                                <AssetDetailsOpenAntivirusAgentModalButton />
                                            </Box>
                                        )}
                                </Stack>
                            )}
                        </Stack>
                    }
                />
            </Stack>
        );
    },
);
