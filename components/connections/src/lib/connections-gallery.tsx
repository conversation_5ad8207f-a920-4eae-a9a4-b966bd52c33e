import { noop } from 'lodash-es';
import { useEffect, useState } from 'react';
import { AppDatatable } from '@components/app-datatable';
import type { ConnectionProps } from '@controllers/connections';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { ActiveConnectionGalleryCard } from './active-connection-gallery-card';
import { AvailableConnectionGalleryCard } from './available-connection-gallery-card';
import { CONNECTIONS_FILTERS } from './constants';
import { filterConnectionsData } from './helpers';

export const ConnectionsGallery = ({
    connectionData,
    tableId,
    isLoading,
    variant,
}: {
    connectionData: ConnectionProps[];
    tableId: string;
    isLoading: boolean;
    variant: 'active' | 'available';
}): React.JSX.Element => {
    const [filteredConnectionData, setFilteredConnectionData] =
        useState(connectionData);
    const [connectionFilters, setConnectionFilters] = useState<
        FetchDataResponseParams | undefined
    >(undefined);

    useEffect(() => {
        const newFilteredData = filterConnectionsData(
            connectionData,
            connectionFilters,
        );

        setFilteredConnectionData(newFilteredData);
    }, [connectionData, connectionFilters]);

    return (
        <Stack
            direction="column"
            gap="4x"
            data-testid="ConnectionsGallery"
            data-id="Bw99ZLAq"
        >
            <AppDatatable
                // isRowSelectionEnabled
                filterProps={CONNECTIONS_FILTERS}
                viewMode="gallery"
                columns={[]}
                isLoading={isLoading}
                tableId={tableId}
                total={filteredConnectionData.length}
                data={filteredConnectionData}
                galleryCard={
                    variant === 'active'
                        ? ActiveConnectionGalleryCard
                        : AvailableConnectionGalleryCard
                }
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: 'Pin filters to page',
                        toggleUnpinnedLabel: 'Move filters to dropdown',
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onRowClick={noop}
                onFetchData={setConnectionFilters}
            />
        </Stack>
    );
};
