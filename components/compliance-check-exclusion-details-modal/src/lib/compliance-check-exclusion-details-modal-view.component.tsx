import type React from 'react';
import { Loader } from '@cosmos/components/loader';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { getFullName } from '@helpers/formatters';
import { ComplianceCheckExclusionDetailsContent } from './compliance-check-exclusion-details-content.component';
import type { ComplianceCheckExclusionDetailsViewProps } from './types/compliance-check-exclusion-details.types';

export const ComplianceCheckExclusionDetailsView = ({
    owner,
    isLoading,
    hasError,
    exclusionDetails,
    onClose,
    'data-id': dataId = 'ComplianceCheckExclusionDetailsView',
}: ComplianceCheckExclusionDetailsViewProps): React.JSX.Element => {
    const personnelFullName = getFullName(owner.firstName, owner.lastName);

    const renderContent = () => {
        if (isLoading) {
            return (
                <Stack direction="column" gap="md">
                    <Loader isSpinnerOnly size="lg" label="" />
                </Stack>
            );
        }

        if (hasError || !exclusionDetails) {
            return (
                <Stack direction="column" gap="md">
                    <Text>{t`Error loading exclusion details`}</Text>
                </Stack>
            );
        }

        return (
            <ComplianceCheckExclusionDetailsContent
                owner={owner}
                exclusionDetails={exclusionDetails}
                data-id={`${dataId}-content`}
                data-testid="renderContent"
            />
        );
    };

    return (
        <>
            <Modal.Header
                title={t`Exclusion for ${personnelFullName}`}
                closeButtonAriaLabel={t`Close modal`}
                onClose={onClose}
            />
            <Modal.Body>{renderContent()}</Modal.Body>
            <Modal.Footer
                leftActionStack={[
                    {
                        label: t`Archive exclusion`,
                        colorScheme: 'danger',
                        level: 'tertiary',
                        onClick: () => {
                            // TODO: Implement archive exclusion functionality
                        },
                    },
                ]}
                rightActionStack={[
                    {
                        label: t`Edit`,
                        level: 'secondary',
                        onClick: () => {
                            // TODO: Implement edit functionality
                        },
                    },
                ]}
            />
        </>
    );
};
