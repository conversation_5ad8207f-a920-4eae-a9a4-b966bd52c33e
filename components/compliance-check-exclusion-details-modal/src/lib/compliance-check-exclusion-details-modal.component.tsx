import type React from 'react';
import { ComplianceCheckExclusionDetailsModalContainer } from './compliance-check-exclusion-details-modal-container.component';
import type { ComplianceCheckExclusionDetailsProps } from './types/compliance-check-exclusion-details.types';

export const ComplianceCheckExclusionDetailsModal = ({
    owner,
    'data-id': dataId = 'ComplianceCheckExclusionDetailsModal',
}: ComplianceCheckExclusionDetailsProps): React.JSX.Element => {
    return (
        <ComplianceCheckExclusionDetailsModalContainer
            owner={owner}
            data-id={dataId}
            data-testid="ComplianceCheckExclusionDetailsModal"
        />
    );
};
