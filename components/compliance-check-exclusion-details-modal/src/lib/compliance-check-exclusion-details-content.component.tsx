import type React from 'react';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { t } from '@globals/i18n/macro';
import { getInitials } from '@helpers/formatters';
import { getComplianceCheckLabel } from './helpers/get-compliance-check-label.helper';
import { isUserPersonnel } from './helpers/is-user-personnel.helper';
import type { ComplianceCheckExclusionDetailsContentProps } from './types/compliance-check-exclusion-details.types';

export const ComplianceCheckExclusionDetailsContent = ({
    owner,
    exclusionDetails,
    'data-id': dataId = 'ComplianceCheckExclusionDetailsContent',
}: ComplianceCheckExclusionDetailsContentProps): React.JSX.Element => {
    const { complianceCheckTypes } = exclusionDetails;
    const { targetDisplayName } = exclusionDetails;
    const { createdBy } = exclusionDetails;
    const { createdAt } = exclusionDetails;
    const { endDate } = exclusionDetails;
    const { reason } = exclusionDetails;

    return (
        <Stack
            direction="column"
            gap="xl"
            data-testid="ComplianceCheckExclusionDetailsContent"
            data-id={dataId}
        >
            {isUserPersonnel(targetDisplayName) ? (
                <KeyValuePair
                    type="REACT_NODE"
                    label={t`Applicable personnel`}
                    value={
                        <AvatarIdentity
                            imgSrc={owner.avatarUrl || undefined}
                            primaryLabel={targetDisplayName}
                            data-testid="ComplianceCheckExclusionDetailsModalPersonnel"
                            data-id={`${dataId}-personnel`}
                            fallbackText={getInitials(targetDisplayName)}
                        />
                    }
                />
            ) : (
                <KeyValuePair
                    type="TEXT"
                    label={t`Applicable personnel`}
                    data-testid="ComplianceCheckExclusionDetailsModalPersonnelText"
                    data-id={`${dataId}-personnel-text`}
                    value={
                        targetDisplayName
                            .toLowerCase()
                            .includes('all personnel')
                            ? t`All personnel - all time`
                            : targetDisplayName
                    }
                />
            )}

            <KeyValuePair
                type="REACT_NODE"
                label={t`Excluded by`}
                value={
                    <AvatarIdentity
                        primaryLabel={createdBy}
                        fallbackText={getInitials(createdBy)}
                        data-testid="ComplianceCheckExclusionDetailsModalExcludedBy"
                        data-id={`${dataId}-excluded-by`}
                    />
                }
            />

            <KeyValuePair
                type="REACT_NODE"
                label={t`Date excluded`}
                value={
                    <DateTime
                        date={createdAt}
                        format="sentence"
                        data-testid="ComplianceCheckExclusionDetailsModalDate"
                        data-id={`${dataId}-date`}
                    />
                }
            />

            <KeyValuePair
                type="REACT_NODE"
                label={t`Compliance checks`}
                value={
                    <Stack wrap="wrap" direction="row" gap="sm">
                        {complianceCheckTypes.map((checkType) => (
                            <Metadata
                                key={checkType.type}
                                label={getComplianceCheckLabel(checkType.type)}
                                data-testid={`ComplianceCheckExclusionDetailsModalComplianceCheck-${checkType.type}`}
                                data-id={`${dataId}-compliance-check-${checkType.type}`}
                            />
                        ))}
                    </Stack>
                }
            />

            <KeyValuePair
                type="REACT_NODE"
                label={t`Duration`}
                value={
                    endDate ? (
                        <DateTime
                            date={createdAt}
                            endDate={endDate}
                            format="table_range"
                            data-testid="ComplianceCheckExclusionDetailsModalDuration"
                            data-id={`${dataId}-duration`}
                        />
                    ) : (
                        <Text>{t`Excluded indefinitely`}</Text>
                    )
                }
            />

            <KeyValuePair
                type="TEXT"
                label={t`Exclusion reason`}
                value={reason}
            />
        </Stack>
    );
};
