import type React from 'react';
import { sharedComplianceCheckExclusionDetailsController } from '@controllers/compliance-check-exclusion-details';
import { modalController } from '@controllers/modal';
import { observer } from '@globals/mobx';
import { ComplianceCheckExclusionDetailsView } from './compliance-check-exclusion-details-modal-view.component';
import { COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID } from './constants/compliance-check-exclusion-details-modal-id.constant';
import type { ComplianceCheckExclusionDetailsContainerProps } from './types/compliance-check-exclusion-details.types';

const handleClose = () => {
    modalController.closeModal(COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID);
};

export const ComplianceCheckExclusionDetailsModalContainer = observer(
    ({
        owner,
        'data-id': dataId = 'ComplianceCheckExclusionDetailsModalContainer',
    }: ComplianceCheckExclusionDetailsContainerProps): React.JSX.Element => {
        const { isLoading, hasError, exclusionDetails } =
            sharedComplianceCheckExclusionDetailsController;

        return (
            <ComplianceCheckExclusionDetailsView
                owner={owner}
                isLoading={isLoading}
                hasError={hasError}
                exclusionDetails={exclusionDetails}
                data-id={dataId}
                onClose={handleClose}
            />
        );
    },
);
