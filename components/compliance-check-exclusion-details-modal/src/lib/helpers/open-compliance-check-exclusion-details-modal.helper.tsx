import { sharedComplianceCheckExclusionDetailsController } from '@controllers/compliance-check-exclusion-details';
import { modalController } from '@controllers/modal';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { action } from '@globals/mobx';
import { ComplianceCheckExclusionDetailsModal } from '../compliance-check-exclusion-details-modal.component';
import { COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID } from '../constants/compliance-check-exclusion-details-modal-id.constant';

export const openComplianceCheckExclusionDetailsModal = action(
    (exclusionId: number, owner: UserResponseDto): void => {
        sharedComplianceCheckExclusionDetailsController.loadExclusionDetails(
            exclusionId,
        );

        modalController.openModal({
            id: COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID,
            content: () => (
                <ComplianceCheckExclusionDetailsModal
                    exclusionId={exclusionId}
                    owner={owner}
                    data-id={`ComplianceCheckExclusionDetailsModal-${exclusionId}`}
                />
            ),
            size: 'md',
            centered: true,
            disableClickOutsideToClose: false,
        });
    },
);
