import { describe, expect, test } from 'vitest';
import { getComplianceCheckLabel } from './get-compliance-check-label.helper';

describe('getComplianceCheckLabel', () => {
    test('should return the original type for unknown compliance check types', () => {
        expect(getComplianceCheckLabel('UNKNOWN_TYPE')).toBe('UNKNOWN_TYPE');
        expect(getComplianceCheckLabel('CUSTOM_CHECK')).toBe('CUSTOM_CHECK');
        expect(getComplianceCheckLabel('')).toBe('');
    });

    test('should handle case sensitivity for unknown types', () => {
        expect(getComplianceCheckLabel('password_manager')).toBe(
            'password_manager',
        );
        expect(getComplianceCheckLabel('Password_Manager')).toBe(
            'Password_Manager',
        );
    });

    test('should return a string for unknown inputs', () => {
        const unknownTypes = [
            'UNKNOWN_TYPE',
            'CUSTOM_CHECK',
            '',
            'random_string',
            'NOT_A_REAL_TYPE',
        ];

        unknownTypes.forEach((type) => {
            const result = getComplianceCheckLabel(type);

            expect(typeof result).toBe('string');
            expect(result).toBe(type);
        });
    });

    test('should have a switch case structure for known types', () => {
        expect(typeof getComplianceCheckLabel).toBe('function');

        const unknownResult = getComplianceCheckLabel('DEFINITELY_UNKNOWN');

        expect(unknownResult).toBe('DEFINITELY_UNKNOWN');
    });
});
