import { describe, expect, test } from 'vitest';
import { isUserPersonnel } from './is-user-personnel.helper';

describe('isUserPersonnel', () => {
    test('should return true for user names', () => {
        expect(isUserPersonnel('<PERSON>')).toBeTruthy();
        expect(isUserPersonnel('<EMAIL>')).toBeTruthy();
        expect(isUserPersonnel('Alice Johnson')).toBeTruthy();
    });

    test('should return false for system values', () => {
        expect(isUserPersonnel('all personnel - all time')).toBeFalsy();
        expect(isUserPersonnel('All Personnel - All Time')).toBeFalsy();
        expect(isUserPersonnel('ALL PERSONNEL')).toBeFalsy();
        expect(isUserPersonnel('tenant')).toBeFalsy();
        expect(isUserPersonnel('ORGANIZATION')).toBeFalsy();
        expect(isUserPersonnel('system')).toBeFalsy();
    });

    test('should handle case insensitive matching', () => {
        expect(isUserPersonnel('TENANT')).toBeFalsy();
        expect(isUserPersonnel('System')).toBeFalsy();
        expect(isUserPersonnel('All Personnel')).toBeFalsy();
    });

    test('should handle partial matches', () => {
        expect(isUserPersonnel('This is all personnel data')).toBeFalsy();
        expect(isUserPersonnel('System administrator')).toBeFalsy();
        expect(isUserPersonnel('Organization settings')).toBeFalsy();
    });
});
