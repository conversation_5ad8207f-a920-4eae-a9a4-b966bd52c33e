import { beforeEach, describe, expect, test, vi } from 'vitest';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID } from '../constants/compliance-check-exclusion-details-modal-id.constant';
import { openComplianceCheckExclusionDetailsModal } from './open-compliance-check-exclusion-details-modal.helper';

const mockOpenModal = vi.hoisted(() => vi.fn());

vi.mock('@controllers/modal', () => ({
    modalController: {
        openModal: mockOpenModal,
    },
}));

describe('openComplianceCheckExclusionDetailsModal', () => {
    const mockOwner: UserResponseDto = {
        id: 1,
        entryId: 'test-entry-id',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        jobTitle: 'Developer',
        avatarUrl: 'https://example.com/avatar.jpg',
        drataTermsAgreedAt: null,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        roles: ['EMPLOYEE'],
    };

    beforeEach(() => {
        mockOpenModal.mockClear();
    });

    test('should call modalController.openModal with correct parameters', () => {
        const exclusionId = 123;

        openComplianceCheckExclusionDetailsModal(exclusionId, mockOwner);

        expect(mockOpenModal).toHaveBeenCalledWith({
            id: COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID,
            content: expect.any(Function),
            size: 'md',
            centered: true,
            disableClickOutsideToClose: false,
        });
    });

    test('should call modalController.openModal exactly once', () => {
        const exclusionId = 456;

        openComplianceCheckExclusionDetailsModal(exclusionId, mockOwner);

        expect(mockOpenModal).toHaveBeenCalledTimes(1);
    });

    test('should call modalController.openModal with correct modal ID', () => {
        const exclusionId = 789;

        openComplianceCheckExclusionDetailsModal(exclusionId, mockOwner);

        expect(mockOpenModal).toHaveBeenCalledWith(
            expect.objectContaining({
                id: COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID,
            }),
        );
    });

    test('should call modalController.openModal with correct configuration', () => {
        const exclusionId = 999;

        openComplianceCheckExclusionDetailsModal(exclusionId, mockOwner);

        expect(mockOpenModal).toHaveBeenCalledWith(
            expect.objectContaining({
                size: 'md',
                centered: true,
                disableClickOutsideToClose: false,
            }),
        );
    });

    test('should call modalController.openModal with content function', () => {
        const exclusionId = 111;

        openComplianceCheckExclusionDetailsModal(exclusionId, mockOwner);

        expect(mockOpenModal).toHaveBeenCalledWith(
            expect.objectContaining({
                content: expect.any(Function),
            }),
        );
    });

    test('should use correct modalId', () => {
        const exclusionId = 456;

        openComplianceCheckExclusionDetailsModal(exclusionId, mockOwner);

        expect(mockOpenModal).toHaveBeenCalledWith(
            expect.objectContaining({
                id: 'compliance-check-exclusion-details-modal',
            }),
        );
    });

    test('should handle owner object with avatar', () => {
        const exclusionId = 789;
        const ownerWithAvatar = {
            ...mockOwner,
            avatarUrl: 'https://example.com/avatar.jpg',
        };

        openComplianceCheckExclusionDetailsModal(exclusionId, ownerWithAvatar);

        expect(mockOpenModal).toHaveBeenCalledWith(
            expect.objectContaining({
                id: COMPLIANCE_CHECK_EXCLUSION_DETAILS_MODAL_ID,
                content: expect.any(Function),
            }),
        );
    });
});
