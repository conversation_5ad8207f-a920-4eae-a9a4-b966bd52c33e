import { t } from '@globals/i18n/macro';

export const getComplianceCheckLabel = (type: string): string => {
    switch (type) {
        case 'ACCEPTED_POLICIES': {
            return t`Acknowledged Policies`;
        }
        case 'IDENTITY_MFA': {
            return t`Identity MFA`;
        }
        case 'BG_CHECK': {
            return t`Background Check`;
        }
        case 'PASSWORD_MANAGER': {
            return t`Password Manager`;
        }
        case 'AUTO_UPDATES': {
            return t`Auto Updates`;
        }
        case 'HDD_ENCRYPTION': {
            return t`Hard Drive Encrypted`;
        }
        case 'ANTIVIRUS': {
            return t`Antivirus`;
        }
        case 'LOCK_SCREEN': {
            return t`Screensaver Lock`;
        }
        case 'SECURITY_TRAINING': {
            return t`Security Training`;
        }
        case 'HIPAA_TRAINING': {
            return t`HIPAA Training`;
        }
        case 'NIST_AI_TRAINING': {
            return t`AI Awareness Training`;
        }
        case 'OFFBOARDING': {
            return t`Offboarding Evidence`;
        }
        default: {
            return type;
        }
    }
};
