import type {
    ComplianceCheckExclusionResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';

export interface ComplianceCheckExclusionDetailsProps {
    exclusionId?: number;
    owner: UserResponseDto;
    'data-id'?: string;
}

export interface ComplianceCheckExclusionDetailsData {
    exclusion: ComplianceCheckExclusionResponseDto;
    isLoading: boolean;
    error: string | null;
}

export interface ComplianceCheckExclusionDetailsContainerProps {
    owner: UserResponseDto;
    'data-id'?: string;
}

export interface ComplianceCheckExclusionDetailsViewProps {
    owner: UserResponseDto;
    isLoading: boolean;
    hasError: boolean;
    exclusionDetails: ComplianceCheckExclusionResponseDto | null;
    onClose: () => void;
    'data-id'?: string;
}
export interface ComplianceCheckExclusionDetailsContentProps {
    owner: UserResponseDto;
    exclusionDetails: ComplianceCheckExclusionResponseDto;
    'data-id'?: string;
}
