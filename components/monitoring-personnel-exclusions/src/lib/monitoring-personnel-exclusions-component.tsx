import { isEmpty } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import {
    activeMonitoringController,
    sharedMonitoringPersonnelExclusionsController,
} from '@controllers/monitoring-details';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import { observer } from '@globals/mobx';
import { MONITORING_PERSONNEL_EXCLUSIONS_COLUMNS } from './monitoring-personnel-exclusions.constants';

export const MonitoringPersonnelExclusionsComponent = observer(
    (): React.JSX.Element => {
        const {
            isLoading,
            exclusionsTotal,
            loadPersonnelExclusions,
            exclusionsPersonnelData,
        } = sharedMonitoringPersonnelExclusionsController;
        const { lastTestResult, isMonitorLoading } = activeMonitoringController;

        if (isLoading || isMonitorLoading) {
            return (
                <Stack direction="column" align="center" gap="6x" p="8x">
                    <Loader isSpinnerOnly label="Loading" />
                </Stack>
            );
        }

        if (lastTestResult === 'ERROR') {
            return (
                <Stack height="100%" justify="center" align="center">
                    <Box width={breakpointMd}>
                        <EmptyState
                            illustrationName="Warning"
                            title="Cannot display exclusions due to error state."
                            description="This test was unable to generate findings due to an error that prevented it from running. Please review the details of the error to resolve it and try again."
                            rightAction={
                                <Button
                                    label="View error details"
                                    level="secondary"
                                />
                            }
                        />
                    </Box>
                </Stack>
            );
        }

        if (isEmpty(exclusionsPersonnelData)) {
            return (
                <Stack height="100%" justify="center" align="center">
                    <Box width={breakpointMd}>
                        <EmptyState
                            illustrationName="NoAccess"
                            title="No exclusions made"
                            description="[Exclusions made will appear here]"
                        />
                    </Box>
                </Stack>
            );
        }

        return (
            <AppDatatable
                // isRowSelectionEnabled
                data-testid="MonitoringPersonnelExclusionsComponent"
                isLoading={isLoading}
                tableId="datatable-personnel-exclusions"
                data-id="datatable-personnel-exclusions"
                data={exclusionsPersonnelData}
                total={exclusionsTotal}
                columns={MONITORING_PERSONNEL_EXCLUSIONS_COLUMNS}
                emptyStateProps={{
                    illustrationName: 'NoAccess',
                    title: 'No exclusions made',
                    description: '[Exclusions made will appear here]',
                }}
                filterViewModeProps={{
                    props: {
                        selectedOption: 'pinned',
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: 'Pin filters to page',
                        toggleUnpinnedLabel: 'Move filters to dropdown',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadPersonnelExclusions}
            />
        );
    },
);
