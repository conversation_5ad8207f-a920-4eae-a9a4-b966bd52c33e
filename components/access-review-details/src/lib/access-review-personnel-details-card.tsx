import { isArray, isEmpty, isNil, isObject } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { Feedback } from '@cosmos/components/feedback';
import type { IconName } from '@cosmos/components/icon';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    codeToJSONString,
    downloadBlob,
    isRawEvidenceDataTooLarge,
    sanitizeFileName,
} from '@helpers/download-file';
import { getInitials } from '@helpers/formatters';
import { AppLink } from '@ui/app-link';
import { filtersByProvider } from '@views/access-review-personnel';
import {
    formatGroupsForDisplay,
    getApplicationLogoSrc,
    shouldShowPermissionLink,
} from './helpers/access-review-personnel-user-card.helper';
import type { AccessReviewPersonnelDetailsCardProps } from './types/access-review-personnel-details-card.types';

export const AccessReviewPersonnelDetailsCard = ({
    applicationDetails,
    accountName,
    email,
    jobTitle,
    employmentStatus,
    groups,
    mfa,
    permissionLink,
    rawAccessData,
    connection,
}: AccessReviewPersonnelDetailsCardProps): React.JSX.Element => {
    const imgSrcLogo = getApplicationLogoSrc(
        applicationDetails.name,
        applicationDetails.logo,
        connection.clientType,
    );
    const warningFiltersObject = filtersByProvider(
        connection.clientType as ClientTypeEnum | null | undefined,
        applicationDetails.source === 'MANUALLY_ADDED',
        applicationDetails.source,
    );

    const handleDownloadAccessData = () => {
        Promise.resolve()
            .then(() => {
                if (isEmpty(rawAccessData)) {
                    throw new Error('Raw access data is not available');
                }

                const fileName = sanitizeFileName(
                    `${new Date().toISOString().split('T')[0]}-${applicationDetails.name}-access-data.txt`,
                );

                const blob = new Blob(
                    [codeToJSONString(JSON.parse(rawAccessData) as object)],
                    { type: 'text/plain' },
                );

                downloadBlob(blob, fileName);
                snackbarController.addSnackbar({
                    id: 'download-access-data-success',
                    props: {
                        title: t`Access data downloaded successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'download-access-data-error',
                    props: {
                        title: t`Failed to download access data`,
                        description: t`An unexpected error occurred`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            });
    };

    const renderGroupsValue = () => {
        if (
            shouldShowPermissionLink(connection.clientType, permissionLink) &&
            !warningFiltersObject.group
        ) {
            return (
                <AppLink
                    isExternal
                    href={permissionLink?.[0] || ''}
                    label={t`View account details`}
                    size="sm"
                />
            );
        }

        const formattedGroups = formatGroupsForDisplay(groups);

        if (!isEmpty(formattedGroups)) {
            return formattedGroups.map((group, index) => {
                const isLastItem = index === formattedGroups.length - 1;
                const groupLabel = isObject(group) ? group.label : group;

                return (
                    <Stack key={groupLabel} data-id="tzwQnWqh">
                        <Text data-id="Y2i-qFHC">{groupLabel}</Text>
                        {!isLastItem && <Text data-id="Y2i-qFHC">,</Text>}
                    </Stack>
                );
            });
        }

        return (
            <EmptyValue
                label="No groups"
                data-testid="renderGroupsValue"
                data-id="jVF49n_2"
            />
        );
    };

    const renderJobValue = () => {
        return (
            <Text data-testid="renderJobValue" data-id="RjslSjv4">
                {jobTitle || '—'}
            </Text>
        );
    };

    const renderAccessData = () => {
        if (!isNil(rawAccessData) && isRawEvidenceDataTooLarge(rawAccessData)) {
            return (
                <KeyValuePair
                    label={t`Access`}
                    type="REACT_NODE"
                    data-testid="renderAccessData"
                    data-id="fO8jxNJ0"
                    value={
                        <Stack direction="column" gap="2x">
                            {!isNil(permissionLink) && (
                                <AppLink
                                    isExternal
                                    size="sm"
                                    href={permissionLink}
                                    label={t`View account details`}
                                />
                            )}
                            <Feedback
                                severity="warning"
                                title={t`The raw user access data is too large to display`}
                                description={t`Download the user access data to review.`}
                                data-id="large-access-data-warning"
                            />
                            <Box>
                                <Button
                                    startIconName="Download"
                                    label={t`Download`}
                                    level="secondary"
                                    data-id="download-button"
                                    size="sm"
                                    onClick={handleDownloadAccessData}
                                />
                            </Box>
                        </Stack>
                    }
                />
            );
        }

        return (
            <KeyValuePair
                label={t`Access`}
                type="REACT_NODE"
                data-testid="renderAccessData"
                data-id="fO8jxNJ0"
                value={
                    <Stack minWidth="100%" direction="column" gap="2x">
                        {!isNil(permissionLink) && (
                            <AppLink
                                isExternal
                                size="sm"
                                href={permissionLink}
                                label={t`View account details`}
                            />
                        )}
                        <CodeViewer
                            data-id="personnel-details-raw-access-data"
                            language="json"
                            value={rawAccessData}
                        />
                    </Stack>
                }
            />
        );
    };

    return (
        <Card
            title={t`Details`}
            size="lg"
            data-testid="AccessReviewPersonnelDetailsCard"
            data-id="c2aGhQJX"
            cardHeight="auto"
            body={
                <Stack gap="4x" direction="column">
                    <KeyValuePair
                        label={t`Application`}
                        type="REACT_NODE"
                        value={
                            <OrganizationIdentity
                                data-id={`${applicationDetails.name}-name-column`}
                                data-testid="AccessReviewApplicationNameCell"
                                primaryLabel={applicationDetails.name}
                                imgSrc={imgSrcLogo}
                                fallbackText={getInitials(
                                    applicationDetails.name,
                                )}
                            />
                        }
                    />
                    {applicationDetails.source !== 'MANUALLY_ADDED' && (
                        <KeyValuePair
                            label={t`Account ID / alias`}
                            type="REACT_NODE"
                            value={
                                <Stack direction="column">
                                    <Text>{connection.accountId}</Text>
                                    <Text>{connection.clientAlias}</Text>
                                </Stack>
                            }
                        />
                    )}
                    <KeyValuePair
                        label={t`Account`}
                        type="TEXT"
                        value={accountName}
                    />
                    {email && (
                        <KeyValuePair
                            label={t`Email`}
                            type="TEXT"
                            value={email}
                        />
                    )}
                    <KeyValuePair
                        label={t`Job title`}
                        type="REACT_NODE"
                        value={renderJobValue()}
                    />
                    <KeyValuePair
                        label={t`Personnel status`}
                        type="TEXT"
                        value={employmentStatus}
                    />

                    <KeyValuePair
                        label={t`Groups`}
                        type="REACT_NODE"
                        visibleItemsLimit={isArray(groups) ? groups.length : 0}
                        value={renderGroupsValue()}
                    />
                    {applicationDetails.source !== 'MANUALLY_ADDED' &&
                        warningFiltersObject.warningFilters.missingMFA &&
                        mfa.feedbackProps && (
                            <KeyValuePair
                                label={t`Multi-Factor Authentication (MFA)`}
                                type="TAG"
                                value={[
                                    {
                                        label: mfa.feedbackProps
                                            .title as string,
                                        colorScheme: mfa.feedbackProps.severity,
                                        type: 'status',
                                        iconName: mfa.iconName as IconName,
                                    },
                                ]}
                            />
                        )}
                    {renderAccessData()}
                </Stack>
            }
        />
    );
};
