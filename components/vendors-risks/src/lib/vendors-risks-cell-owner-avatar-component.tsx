import { isEmpty } from 'lodash-es';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { getFullName, getInitials } from '@helpers/formatters';
import type { VendorsRisksCellOwnerAvatarComponentProps } from './types/vendors-risks.types';

const MAX_OWNERS_TO_SHOW = 6;

export const VendorsRisksCellOwnerAvatarComponent = ({
    row,
}: VendorsRisksCellOwnerAvatarComponentProps): React.JSX.Element => {
    const { owners } = row.original;

    if (isEmpty(owners)) {
        return <EmptyValue label="-" />;
    }

    return (
        <AvatarStack
            data-id="4TAo3lAW"
            maxVisibleItems={MAX_OWNERS_TO_SHOW}
            data-testid="VendorsRisksCellOwnerAvatarComponent"
            avatarData={owners.map((owner) => ({
                fallbackText: getInitials(
                    getFull<PERSON><PERSON>(owner.firstName, owner.lastName),
                ),
                primaryLabel: getFull<PERSON><PERSON>(owner.firstName, owner.lastName),
                secondaryLabel: owner.email,
                imgSrc: owner.avatarUrl,
            }))}
        />
    );
};
