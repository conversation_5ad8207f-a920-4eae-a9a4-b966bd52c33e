import { sharedVendorsCreateCurrentVendorController } from '@controllers/vendors';
import { Banner } from '@cosmos/components/banner';
import { Loader } from '@cosmos/components/loader';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const VendorDetailsFinishFormComponent = observer(
    (): React.JSX.Element => {
        const { isLoading, vendorId, vendorName } =
            sharedVendorsCreateCurrentVendorController;

        const errorMessage = t`We couldn't find any vendors to add or update due to missing, invalid or duplicated data. Follow the Help Article instructions and reupload a new file.`;

        if (isLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        if (!vendorId) {
            return (
                <Banner
                    displayMode="section"
                    severity="critical"
                    title={t`Error`}
                    body={errorMessage}
                />
            );
        }

        return (
            <Text
                data-testid="VendorDetailsFinishFormComponent"
                data-id="finishStep"
                size="400"
            >
                {t`${vendorName} has been added as a current vendor!`}
            </Text>
        );
    },
);
