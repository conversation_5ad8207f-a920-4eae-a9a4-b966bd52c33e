import type { TransformedVendorSecurityReviewObservation } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { UtilitiesCommentComponent } from './comments/utilities-notes-comment-component';
import { NoteCommentForm } from './comments/utilities-notes-comment-form-component';

export interface ObservationCreateDto {
    observation: string;
    source: string;
}

export interface UtilitiesObservationsBaseComponentProps {
    observations: TransformedVendorSecurityReviewObservation[];
    title?: string;
    subtitle?: string;
    onCreate?: (values: ObservationCreateDto) => void;
    onUpdate?: (
        observationId: string,
        values: { observation: string; source: string },
    ) => void;
    onDelete?: (observationId: string) => void;
    isReadOnly?: boolean;
    showAddAttachment?: 'modal' | 'field';
    hasSource?: boolean;
}

export const UtilitiesObservationsBaseComponent = observer(
    ({
        observations = [],
        title = 'Observations',
        subtitle = '',
        onCreate,
        onUpdate,
        onDelete,
        isReadOnly = false,
        showAddAttachment = undefined,
        hasSource = true,
    }: UtilitiesObservationsBaseComponentProps): React.JSX.Element => {
        return (
            <Stack
                direction="column"
                height="100%"
                width="100%"
                data-testid="UtilitiesObservationsComponent"
                data-id="9329QCnO"
                py="xl"
                pl="xl"
                pr="2xl"
                overflowY="auto"
            >
                <Stack gap="2x" direction="column" pb="lg">
                    <Text size="300" type="title">
                        {title}
                    </Text>
                    <Text size="100">{subtitle}</Text>
                </Stack>
                <Stack width="100%" direction="column">
                    {isReadOnly || (
                        <NoteCommentForm
                            hasSource={hasSource}
                            attachments={[]}
                            comment=""
                            source=""
                            commentLabel="New observation"
                            attachmentConfig={{ showAddAttachment }}
                            editMode={false}
                            labels={{ confirmationButton: 'Save' }}
                            onSubmit={(values) => {
                                const newObservation: ObservationCreateDto = {
                                    observation: values.comment,
                                    source: values.source,
                                };

                                onCreate?.(newObservation);
                            }}
                        />
                    )}
                    {observations.map((observation, index) => {
                        const hasDivider = index < observations.length - 1;

                        return (
                            <UtilitiesCommentComponent
                                hasSecondaryInput
                                key={`${observation.id}-comment`}
                                id={observation.id.toString()}
                                formId={`${observation.id}-observation-form-id`}
                                value={observation.observation}
                                createdAt={observation.createdAt}
                                imgSrc={observation.user?.avatarUrl ?? ''}
                                isReadOnly={isReadOnly}
                                hasDivider={hasDivider}
                                data-id={`${observation.id}-utility-observation-comment`}
                                secondaryInputLabel="Source:"
                                commentFieldLabel="Observation"
                                secondaryInputValue={observation.source}
                                attachments={[]}
                                labels={{
                                    confirmationButton: 'Update',
                                }}
                                identityName={getFullName(
                                    observation.user?.firstName,
                                    observation.user?.lastName,
                                )}
                                onSave={(updateObservation) => {
                                    onUpdate?.(observation.id.toString(), {
                                        observation: updateObservation.comment,
                                        source: observation.source,
                                    });
                                }}
                                onDelete={() => {
                                    onDelete?.(observation.id.toString());
                                }}
                            />
                        );
                    })}
                </Stack>
            </Stack>
        );
    },
);
