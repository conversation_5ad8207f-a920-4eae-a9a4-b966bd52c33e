import { sharedAccessReviewActiveApplicationNotesController } from '@controllers/access-reviews';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForActiveApplicationComponent = observer(
    (): React.JSX.Element => {
        return (
            <UtilitiesNotesBaseComponent
                notes={sharedAccessReviewActiveApplicationNotesController.list}
                data-id="ZFuoioNg"
                labels={{
                    title: t`Internal notes`,
                    subtitle: t`Add any feedback or questions you want to track for this application. Reviewers will receive an email notification. These messages are not shared with auditors.`,
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this application.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this application. These messages are not shared with auditors.`,
                }}
                attachments={{
                    showAddAttachment: 'field',
                    acceptedFormats: [
                        'pdf',
                        'docx',
                        'odt',
                        'xlsx',
                        'ods',
                        'pptx',
                        'odp',
                    ],
                }}
                isReadOnly={
                    sharedAccessReviewActiveApplicationNotesController.isReadOnly
                }
                onCreate={(values) => {
                    runInAction(() => {
                        sharedAccessReviewActiveApplicationNotesController.createNote(
                            {
                                comment: values.comment || '',
                                files: values['files[]'] ?? [],
                            },
                        );
                    });
                }}
                onUpdate={(noteId, values) => {
                    runInAction(() => {
                        sharedAccessReviewActiveApplicationNotesController.updateNote(
                            noteId,
                            {
                                comment: values.comment || '',
                                files: values['files[]'] ?? [],
                            },
                        );
                    });
                }}
                onDelete={(noteId) => {
                    runInAction(() => {
                        sharedAccessReviewActiveApplicationNotesController.deleteNote(
                            noteId,
                        );
                    });
                }}
                onDownloadAttachment={(noteFileId: string) => {
                    runInAction(() => {
                        sharedAccessReviewActiveApplicationNotesController.downloadNoteAttachment(
                            noteFileId,
                        );
                    });
                }}
            />
        );
    },
);
