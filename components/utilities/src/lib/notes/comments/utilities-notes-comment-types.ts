import type { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'react';
import type { SupportedFormat } from '@cosmos/components/file-upload';
import type { NoteFileResponseDto } from '@globals/api-sdk/types';
import type { NoteUpdateDto } from '../utilities-notes-create-dto-types';

export interface CommentProps {
    /**
     * A unique identifier for the form this field is a part of.
     */
    id: string;

    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;

    /**
     * A unique identifier for the form this field is a part of.
     */
    formId: string;

    /**
     * Message to show when component is readOnly.
     */
    value: string;

    /**
     * Date when comment/note was created.
     */
    createdAt: string;

    /**
     * Name to display in the identity.
     */
    identityName: string;

    /**
     * URL of identity image.
     */
    imgSrc: string;

    /**
     * Whether the Comments component should be readOnly.
     */
    isReadOnly: boolean;

    /**
     * Data to display in the comment as attached files.
     */
    attachments: NoteFileResponseDto[];

    /**
     * Message error to show.
     */
    feedbackMessage?: string;

    /**
     * Selects whether it is necessary to display the divider.
     */
    hasDivider?: boolean;

    /**
     * Selects whether it is necessary to display the secondary input.
     */
    hasSecondaryInput?: boolean;

    /**
     * Label to display in the secondary input.
     */
    secondaryInputLabel?: string;

    /**
     * Value to display in the secondary input.
     */
    secondaryInputValue?: string;

    /**
     * Message error to show in the secondary input.
     */
    feedbackMessageSecondaryInput?: string;

    /**
     * Label to display in the comment input.
     */
    commentFieldLabel?: string;

    /**
     * Label to display in the source input.
     */
    sourceFieldLabel?: string;

    /**
     * Labels for customization.
     */
    labels?: {
        confirmationButton?: string;
        editTooltip?: string;
        deleteTooltip?: string;
        cancelConfirmationBody?: string;
    };

    /**
     * Attachment configuration.
     */
    attachmentConfig?: {
        shouldShowAddAttachment?: 'modal' | 'field';
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
    };

    /**
     * Function called when value changes.
     */
    onChange?: ChangeEventHandler<HTMLTextAreaElement>;

    /**
     * Function called when want to save a new/edited comment.
     */
    onSave: (note: NoteUpdateDto) => void;

    /**
     * Function called when want to cancel the creation/edition of a comment.
     */
    onCancel?: () => void;

    /**
     * Function called when want delete a comment.
     */
    onDelete: (id: string) => void;

    /**
     * Function called when want to download a file.
     */
    onDownload?: (id: number) => void;

    /**
     * Function called when want to upload a file.
     */
    onUploadFile?: () => void;

    /**
     * Function called when want to delete a file.
     */
    onDeleteFile?: (id: number) => void;

    /**
     * Function called when want to download an attachment.
     */
    onDownloadAttachment?: (noteFileId: string) => void;

    /**
     * Function called when want to change the secondary input.
     */
    onChangeSecondaryInput?: ChangeEventHandler<HTMLTextAreaElement>;

    /**
     * Whether this is for messages (true) or notes (false).
     * When true, attachment modals won't show title/created at fields.
     */
    isMessages?: boolean;

    /**
     * Whether the message has been read (only applicable when isMessages is true).
     * When true and isMessages is true, shows a red dot indicator.
     */
    hasBeenRead?: boolean;

    /**
     * Function called when want to update message read status.
     * Only applicable when isMessages is true.
     */
    onUpdateReadStatus?: (id: string, hasBeenRead: boolean) => void;
}
