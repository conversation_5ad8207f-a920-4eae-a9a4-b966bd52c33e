import { isEmpty } from 'lodash-es';
import { modalController } from '@controllers/modal';
import type { SupportedFormat } from '@cosmos/components/file-upload';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { Form, type FormSchema, useFormSubmit } from '@ui/forms';
import { UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID } from './utilities-notes-comment-attachment-form-constant';

/**
 * 5MB.
 */
const MAX_FILE_SIZE_IN_BYTES = 1024 * 1024 * 5;

const handleCloseModal = action(() => {
    modalController.closeModal(
        UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID,
    );
});

const getSchema = (
    attachmentConfig: {
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
    } = {},
): FormSchema => {
    const {
        acceptedFormats = ['jpeg', 'png'],
        useSimpleAttachments = false,
        includeAttachmentTitle = false,
        includeAttachmentCreationDate = false,
    } = attachmentConfig;
    const baseSchema: FormSchema = {
        file: {
            type: 'file',
            label: t`Attachments`,
            initialValue: [],
            maxFileSizeInBytes: MAX_FILE_SIZE_IN_BYTES,
            selectButtonText: t`Select file`,
            removeButtonText: t`Remove file`,
            acceptedFormats,
            oneFileOnly: true,
            errorCodeMessages: {
                'file-invalid-type': t`Not a valid file type.`,
                'file-too-large': t`File size is too large.`,
                'file-too-small': t`File size is too small.`,
                'too-many-files': t`Contains too many files.`,
            },
            innerLabel: t`Or drop files here`,
            isMulti: false,
        },
    };

    // Add conditional fields based on flags
    if (!useSimpleAttachments) {
        if (includeAttachmentTitle) {
            baseSchema.name = {
                type: 'text',
                label: t`Title`,
            };
        }
        if (includeAttachmentCreationDate) {
            baseSchema.creationDate = {
                type: 'date',
                label: t`Created at`,
                getIsDateUnavailable: (date: string) => {
                    const selectedDate = new Date(date);
                    const today = new Date();

                    today.setHours(0, 0, 0, 0);

                    return selectedDate > today;
                },
            };
        }
    }

    return baseSchema;
};

export const UtilitiesNotesCommentAttachmentFormModal = ({
    onSubmit,
    attachmentConfig = {},
}: {
    onSubmit: ({
        name,
        creationDate,
        file,
    }: {
        name: string;
        creationDate: string;
        file: File;
    }) => void;
    attachmentConfig?: {
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
    };
}): React.JSX.Element => {
    const { useSimpleAttachments = false, includeAttachmentTitle = false } =
        attachmentConfig;

    const schema = getSchema(attachmentConfig);
    const { formRef, triggerSubmit } = useFormSubmit();

    return (
        <>
            <Modal.Header
                title={t`Add attachment`}
                closeButtonAriaLabel={t`Close add attachment modal`}
                onClose={handleCloseModal}
            />
            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="modal-form"
                    ref={formRef}
                    schema={schema}
                    formId="modal-form"
                    onSubmit={({ name, creationDate, file }) => {
                        if (isEmpty(file)) {
                            return;
                        }

                        const selectedFile = (file as File[])[0] ?? null;

                        onSubmit({
                            name:
                                useSimpleAttachments || !includeAttachmentTitle
                                    ? selectedFile.name
                                    : (name as string) || selectedFile.name,
                            creationDate:
                                (creationDate as string) ||
                                new Date().toISOString(),
                            file: selectedFile,
                        });
                        handleCloseModal();
                    }}
                />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'tertiary',
                        onClick: handleCloseModal,
                    },
                    {
                        label: t`Complete`,
                        level: 'primary',
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                // not handling errors here
                                console.error(t`Failed to submit form`);
                            });
                        },
                    },
                ]}
            />
        </>
    );
};
