import type { SupportedFormat } from '@cosmos/components/file-upload';
import { UtilitiesNotesCommentAttachmentFormModal } from './utilities-notes-comment-attachment-form-modal';

export const AttachmentFormModalContent = ({
    onSubmit,
    attachmentConfig = {},
}: {
    onSubmit: (attachment: {
        name: string;
        creationDate: string;
        file: File;
    }) => void;
    attachmentConfig?: {
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
    };
}): React.JSX.Element => (
    <UtilitiesNotesCommentAttachmentFormModal
        data-id="XdvBsNTv"
        data-testid="AttachmentFormModalContent"
        attachmentConfig={attachmentConfig}
        onSubmit={onSubmit}
    />
);
