import { sharedAccessReviewActivePeriodUserNotesController } from '@controllers/access-reviews';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForActivePeriodUserComponent = observer(
    (): React.JSX.Element => {
        const { list: notes } =
            sharedAccessReviewActivePeriodUserNotesController;

        return (
            <UtilitiesNotesBaseComponent
                notes={notes}
                data-id="zBsh_UOr"
                labels={{
                    title: t`Internal notes`,
                    subtitle: t`Add any feedback or questions you want to track for events. Event owners will receive an email notification. These messages are not shared with auditors.`,
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this period.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this period. These messages are not shared with auditors.`,
                }}
                attachments={{
                    showAddAttachment: 'field',
                    acceptedFormats: [
                        'pdf',
                        'docx',
                        'odt',
                        'xlsx',
                        'ods',
                        'pptx',
                        'odp',
                    ],
                }}
                onCreate={(values) => {
                    runInAction(() => {
                        sharedAccessReviewActivePeriodUserNotesController.createNote(
                            {
                                comment: values.comment || '',
                                files: values['files[]'] ?? [],
                            },
                        );
                    });
                }}
                onUpdate={(noteId, values) => {
                    runInAction(() => {
                        sharedAccessReviewActivePeriodUserNotesController.updateNote(
                            noteId,
                            {
                                comment: values.comment || '',
                                files: values['files[]'] ?? [],
                            },
                        );
                    });
                }}
                onDelete={(noteId) => {
                    runInAction(() => {
                        sharedAccessReviewActivePeriodUserNotesController.deleteNote(
                            noteId,
                        );
                    });
                }}
                onDownloadAttachment={(noteFileId: string) => {
                    runInAction(() => {
                        sharedAccessReviewActivePeriodUserNotesController.downloadNoteAttachment(
                            noteFileId,
                        );
                    });
                }}
            />
        );
    },
);
