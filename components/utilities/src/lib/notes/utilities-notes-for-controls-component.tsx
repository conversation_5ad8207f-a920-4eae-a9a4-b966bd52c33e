import { sharedEventsNotesController } from '@controllers/events-details';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForControlsComponent = observer(
    (): React.JSX.Element => {
        return (
            <UtilitiesNotesBaseComponent
                notes={[]}
                data-id="zBsh_UOr"
                labels={{
                    title: 'Internal notes',
                    subtitle:
                        'Add any feedback or questions you want to track for controls. Control owners will receive an email notification. These messages are not shared with auditors.',
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this control.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this control. These messages are not shared with auditors.`,
                }}
                onCreate={(values) => {
                    sharedEventsNotesController.createNote(values);
                }}
                onUpdate={(noteId, values) => {
                    sharedEventsNotesController.updateNote(noteId, values);
                }}
                onDelete={(noteId) => {
                    sharedEventsNotesController.deleteNote(noteId);
                }}
                onDownloadAttachment={(noteFileId: string) => {
                    sharedEventsNotesController.downloadNoteAttachment(
                        noteFileId,
                    );
                }}
            />
        );
    },
);
