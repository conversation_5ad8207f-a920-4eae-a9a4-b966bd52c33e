import type {
    NoteRequestDto,
    NoteResponseDto,
    UserCardResponseDto,
} from '@globals/api-sdk/types';

export interface NoteCreateDto {
    'files[]'?: (Blob | File)[];
    fileMetadata?: {
        /**
         * The original name of the file.
         */
        originalFile?: string;
        /**
         * The name to use for the file.
         */
        name?: string;
        /**
         * The time the file was created.
         */
        creationDate?: string;
    }[];
    /**
     * The comment to use for the note.
     */
    comment?: string;
}

export interface NoteUpdateDto extends NoteRequestDto {
    /**
     * Array of attachment IDs to delete.
     */
    filesToDelete?: string[];
    /**
     * Files to upload.
     */
    'files[]'?: (File | Blob)[];
    /**
     * File metadata for messages.
     */
    fileMetadata?: {
        /**
         * The original name of the file.
         */
        originalFile: string;
        /**
         * The name to use for the file.
         */
        name?: string | null;
        /**
         * The time the file was created.
         */
        creationDate: string;
    }[];
}

// Message-specific types that extend the base API types with additional properties
export type MessageUserCardResponseDto = UserCardResponseDto & {
    /**
     * Indicates if the message author is an auditor.
     */
    authorIsAuditor?: boolean;
};

export type MessageNoteResponseDto = NoteResponseDto & {
    /**
     * Indicates if the message has been read by the recipient.
     */
    hasBeenRead?: boolean;
    /**
     * Message owner with auditor information.
     */
    owner: MessageUserCardResponseDto;
};
