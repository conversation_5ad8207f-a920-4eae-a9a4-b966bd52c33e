import { sharedCustomerRequestUtilitiesNotesController } from '@controllers/utilities';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForCustomerRequestsComponent = observer(
    (): React.JSX.Element => {
        return (
            <UtilitiesNotesBaseComponent
                enableAuditorOnlyEditing
                showUnreadIndicators
                enableReadStatusTracking
                notes={sharedCustomerRequestUtilitiesNotesController.messages}
                data-id="zBsh_UOr"
                attachments={{
                    showAddAttachment: 'modal',
                    useSimpleAttachments: true,
                    includeAttachmentTitle: false,
                    includeAttachmentCreationDate: false,
                    acceptedFormats: [
                        'pdf',
                        'jpeg',
                        'docx',
                        'odt',
                        'xlsx',
                        'ods',
                        'pptx',
                        'odp',
                        'png',
                        'gif',
                        'jpg',
                        'csv',
                        'txt',
                        'json',
                        'zip',
                        'svg',
                    ],
                }}
                labels={{
                    title: 'Messages',
                    subtitle: '',
                    commentLabel: t`New message`,
                    emptyStateTitle: t`No messages yet`,
                    emptyStateDescription: t`Start a conversation by adding your first message above.`,
                    readOnlyEmptyStateDescription: t`Follow the conversation about this request.`,
                    addButton: t`Add message`,
                    saveButton: t`Save message`,
                    editTooltip: t`Edit message`,
                    deleteTooltip: t`Delete message`,
                    cancelConfirmationBody: t`If you want to keep the changes you've made, cancel and save message before closing.`,
                }}
                onCreate={(values) => {
                    sharedCustomerRequestUtilitiesNotesController.createNote(
                        values,
                    );
                }}
                onUpdate={(noteId, values) => {
                    sharedCustomerRequestUtilitiesNotesController.updateNote(
                        noteId,
                        values,
                    );
                }}
                onDelete={(noteId) => {
                    sharedCustomerRequestUtilitiesNotesController.deleteNote(
                        noteId,
                    );
                }}
                onDownloadAttachment={(noteFileId: string) => {
                    sharedCustomerRequestUtilitiesNotesController.downloadNoteAttachment(
                        noteFileId,
                    );
                }}
                onUpdateReadStatus={(noteId: string, hasBeenRead: boolean) => {
                    sharedCustomerRequestUtilitiesNotesController.updateMessageReadStatus(
                        noteId,
                        hasBeenRead,
                    );
                }}
            />
        );
    },
);
