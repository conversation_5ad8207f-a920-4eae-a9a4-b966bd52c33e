import { sharedEventsNotesController } from '@controllers/events-details';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';

export const UtilitiesNotesForEventsComponent = observer(
    (): React.JSX.Element => {
        const { list: notes } = sharedEventsNotesController;

        return (
            <UtilitiesNotesBaseComponent
                notes={notes}
                data-id="zBsh_UOr"
                attachments={{
                    showAddAttachment: 'modal',
                    includeAttachmentCreationDate: false,
                    includeAttachmentTitle: true,
                }}
                labels={{
                    title: t`Internal notes`,
                    subtitle: t`Add any feedback or questions you want to track for events. These messages are not shared with auditors.`,
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this event.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this event. These messages are not shared with auditors.`,
                }}
                onCreate={(values) => {
                    sharedEventsNotesController.createNote(values);
                }}
                onUpdate={(noteId, values) => {
                    sharedEventsNotesController.updateNote(noteId, values);
                }}
                onDelete={(noteId) => {
                    sharedEventsNotesController.deleteNote(noteId);
                }}
                onDownloadAttachment={(noteFileId: string) => {
                    sharedEventsNotesController.downloadNoteAttachment(
                        noteFileId,
                    );
                }}
            />
        );
    },
);
