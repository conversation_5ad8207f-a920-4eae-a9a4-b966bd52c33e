import { sharedConnectionsController } from '@controllers/connections';
import {
    activeTrackCardController,
    sharedMonitorFindingsController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Card } from '@cosmos/components/card';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { NextStepsBody } from './next-steps-body';

export const NextStepsCard = observer((): React.JSX.Element => {
    const { isLoading: isConnectionsLoading } = sharedConnectionsController;
    const { isLoading: isTestDetailsLoading } =
        sharedMonitoringTestDetailsController;
    const { isLoading: isFindingsLoading } = sharedMonitorFindingsController;
    const { isLoading: isTrackLoading } = activeTrackCardController;

    const isLoading =
        isFindingsLoading ||
        isConnectionsLoading ||
        isTestDetailsLoading ||
        isTrackLoading;

    return (
        <Card
            size="lg"
            data-id="overview-next-steps-card"
            title="Next steps"
            data-testid="NextStepsCard"
            body={
                isLoading ? (
                    <Stack justify="center">
                        <Loader label="Loading..." />
                    </Stack>
                ) : (
                    <NextStepsBody />
                )
            }
        />
    );
});
