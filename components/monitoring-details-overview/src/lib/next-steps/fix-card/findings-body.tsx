import { sharedMonitorFindingsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Button } from '@cosmos/components/button';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { useNavigate } from '@remix-run/react';

export const FindingsBody = (): React.JSX.Element => {
    const navigate = useNavigate();
    const { testDetails } = sharedMonitoringTestDetailsController;
    const { failingResources } = sharedMonitorFindingsController;

    return (
        <>
            <StatBlock
                title="Failing resources"
                statValue={failingResources}
                statValueColor="critical"
                state="static"
                statIcon="Cancel"
                statIconColor="critical"
                trendDirection="down"
                trendSentiment="negative"
            />

            <Button
                hasPadding={false}
                label="View findings"
                level="tertiary"
                size="sm"
                onClick={() => {
                    navigate(
                        `/compliance/monitoring/details/${testDetails?.testId}/findings`,
                    );
                }}
            />
        </>
    );
};
