import { sharedMonitorFindingsController } from '@controllers/monitoring-details';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import { FindingsBody } from './findings-body';
import { NoFindingsBody } from './no-findings-body';

export const FixCard = observer((): React.JSX.Element => {
    const { monitorHasFindings } = sharedMonitorFindingsController;

    return (
        <Box
            data-id="q6JvnF-e"
            borderRadius="borderRadiusLg"
            borderColor="neutralBorderFaded"
            borderWidth="borderWidth1"
            p="lg"
            width="100%"
        >
            <Stack direction="column" align="start" height="100%" gap="md">
                <Text type="title" size="200">
                    Fix
                </Text>
                {monitorHasFindings ? <FindingsBody /> : <NoFindingsBody />}
            </Stack>
        </Box>
    );
});
