import { isNil } from 'lodash-es';
import { OutOfScopeModal } from '@components/out-of-scope-modal';
import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import {
    sharedRequirementDetailsController,
    sharedRequirementsController,
} from '@controllers/requirements';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import {
    criticalBackgroundStrongInitial,
    dimensionSm,
    successBackgroundModerate,
} from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { DataDonut } from '@cosmos-lab/components/data-donut';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, when } from '@globals/mobx';

const OUT_OF_SCOPE_CONTROLS_MODAL_ID = 'out-of-scope-controls-modal';

export class RequirementDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get breadcrumbs(): Breadcrumb[] {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        if (!frameworkDetails) {
            return [
                {
                    label: t`Frameworks`,
                    /**
                     * Example:
                     * from - /workspaces/1/compliance/frameworks/all/current/3/requirements/202/overview
                     * to   - /workspaces/1/compliance/frameworks/all/current.
                     */ pathname: window.location.pathname
                        .split('/')
                        .slice(0, -4)
                        .join('/'),
                },
            ];
        }

        return [
            {
                label: t`Frameworks`,
                /**
                 * Example:
                 * from - /workspaces/1/compliance/frameworks/all/current/3/requirements/202/overview
                 * to   - /workspaces/1/compliance/frameworks/all/current.
                 */
                pathname: window.location.pathname
                    .split('/')
                    .slice(0, -4)
                    .join('/'),
            },
            {
                label: frameworkDetails.slug.toUpperCase(),
                /**
                 * Example:
                 * from - /workspaces/1/compliance/frameworks/all/current/3/requirements/202/overview
                 * to   - /workspaces/1/compliance/frameworks/all/current/3/requirements.
                 */
                pathname: window.location.pathname
                    .split('/')
                    .slice(0, -2)
                    .join('/'),
            },
        ];
    }

    get isLoading(): boolean {
        return (
            sharedFrameworkDetailsController.isLoading ||
            sharedRequirementDetailsController.isRequirementLoading
        );
    }

    get title(): string {
        const { requirement } = sharedRequirementDetailsController;

        if (!requirement) {
            return '';
        }

        return requirement.description;
    }

    get slot(): React.JSX.Element | null {
        const { requirement } = sharedRequirementDetailsController;

        if (!requirement) {
            return null;
        }

        const { isReady } = requirement;
        const label = isReady ? t`Ready` : t`Not Ready`;

        return (
            <Metadata
                colorScheme={isReady ? 'success' : 'critical'}
                iconName={isReady ? 'CheckCircle' : 'Cancel'}
                label={label}
                type="tag"
                data-id="sfJW27Us"
            />
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { requirement } = sharedRequirementDetailsController;
        const { frameworkDetails } = sharedFrameworkDetailsController;

        if (!requirement || !frameworkDetails) {
            return [];
        }
        const { totalInScopeControls, numReadyInScopeControls } =
            frameworkDetails;
        const ready = numReadyInScopeControls;

        return [
            {
                id: 'controls-ready',
                'data-id': 'ddfEErop',
                label: t`Controls Ready`,
                value: (
                    <Stack gap="sm">
                        <DataDonut
                            data-id="dashboard-policies-donut"
                            size="sm"
                            unit={t`Controls`}
                            align="center"
                            values={[
                                {
                                    label: t`Active`,
                                    value: ready,
                                    color: successBackgroundModerate,
                                },
                                {
                                    label: t`Total`,
                                    value: totalInScopeControls,
                                    color: criticalBackgroundStrongInitial,
                                },
                            ]}
                        />
                        <Stack direction="column" justify="center">
                            <Text>
                                {ready}/{totalInScopeControls}
                            </Text>
                        </Stack>
                    </Stack>
                ),
                type: 'REACT_NODE',
            },
            {
                id: 'code',
                'data-id': 'ddfEErop',
                label: t`Code`,
                value: requirement.name,
                type: 'TEXT',
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        const { requirement, requirementDetailsQuery } =
            sharedRequirementDetailsController;

        if (!requirement) {
            return (
                <ActionStack
                    gap={dimensionSm}
                    stacks={[
                        {
                            actions: [],
                            id: 'map-controls-action-stack',
                        },
                    ]}
                />
            );
        }

        const isOutOfScope = !isNil(requirement.archivedAt);

        const actions: Action[] = [];

        const handleInScope = action(() => {
            sharedRequirementsController.updateRequirementsScope(
                [requirement.id],
                true,
            );
            when(
                () => !sharedRequirementsController.isUpdatingScope,
                () => {
                    requirementDetailsQuery.invalidate();
                },
            );
        });

        const handleOutOfScope = action((reason: string) => {
            sharedRequirementsController.updateRequirementsScope(
                [requirement.id],
                false,
                reason,
            );
            when(
                () => !sharedRequirementsController.isUpdatingScope,
                () => {
                    requirementDetailsQuery.invalidate();
                },
            );
            modalController.closeModal(OUT_OF_SCOPE_CONTROLS_MODAL_ID);
        });

        if (isOutOfScope) {
            actions.push({
                actionType: 'button',
                id: 'in-scope-controls-action-stack',
                typeProps: {
                    label: t`Mark requirement in scope`,
                    onClick: handleInScope,
                },
            });
        } else {
            actions.push({
                actionType: 'button',
                id: 'out-of-scope-controls-action-stack',
                typeProps: {
                    label: t`Mark out of scope`,
                    onClick: () => {
                        modalController.openModal({
                            id: OUT_OF_SCOPE_CONTROLS_MODAL_ID,
                            content: () => (
                                <OutOfScopeModal
                                    data-id="disable-requirement-modal"
                                    title={t`Marking Requirements Out of Scope`}
                                    onConfirm={handleOutOfScope}
                                    onCancel={() => {
                                        modalController.closeModal(
                                            OUT_OF_SCOPE_CONTROLS_MODAL_ID,
                                        );
                                    }}
                                >
                                    {t`Marking Requirements Out of Scope`}
                                </OutOfScopeModal>
                            ),
                            centered: true,
                            disableClickOutsideToClose: true,
                            size: 'md',
                        });
                    },
                },
            });
        }

        return (
            <ActionStack
                gap={dimensionSm}
                stacks={[
                    {
                        actions,
                        id: 'map-controls-action-stack',
                    },
                ]}
            />
        );
    }
}
