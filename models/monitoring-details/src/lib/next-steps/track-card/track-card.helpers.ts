import { isArray, isNil } from 'lodash-es';
import type { ConnectionProps } from '@controllers/connections';
import type { MonitorTrackResponse } from '@controllers/monitoring-details';
import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';
import { formatDate } from '@helpers/date-time';

export const buildTrackTitle = ({
    hasTicketingConnection,
    trackStatus,
    errorTitle,
}: {
    hasTicketingConnection: boolean;
    trackStatus: MonitorTrackResponse['status'];
    errorTitle: string;
}): string => {
    const defaultTitle = 'Track';

    if (!trackStatus) {
        return defaultTitle;
    }

    if (!hasTicketingConnection && trackStatus === 'PASSED') {
        return "You're good to go!";
    }

    if (trackStatus === 'ERROR' && errorTitle) {
        return errorTitle;
    }

    return defaultTitle;
};

export const getConnectionIdsFromControlTest = (
    controlTest: MonitorV2ControlTestInstanceOverviewResponseDto | null,
): number[] => {
    const connectionIdsSet = new Set<number>();

    if (!isArray(controlTest?.monitorInstances)) {
        return [];
    }

    controlTest.monitorInstances.forEach((monitorInstance) => {
        if (isArray(monitorInstance.metadata)) {
            monitorInstance.metadata.forEach((meta) => {
                if (!isNil(meta.connectionId)) {
                    connectionIdsSet.add(meta.connectionId);
                }
            });
        }
    });

    return [...connectionIdsSet];
};

export const getIsAnyConnectionMisconfigurated = (
    connections: ConnectionProps[],
): boolean => {
    return connections.some(
        (connection) => connection.state === 'MISCONFIGURED',
    );
};

export const formatTrackSinceFromDate = (
    date: string | null | undefined,
): string => {
    if (isNil(date)) {
        return '';
    }

    return `since ${formatDate('field_time', date)}`;
};
