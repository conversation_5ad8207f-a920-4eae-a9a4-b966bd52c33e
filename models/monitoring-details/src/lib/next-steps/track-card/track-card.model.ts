import { chain, isEmpty, isEqual, isNil } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import {
    activeTrackCardController,
    mapStatusToTrackStatus,
    type MonitorTrackStatus,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { ColorScheme } from '@cosmos/components/text';
import { eventsControllerListEventsOptions } from '@globals/api-sdk/queries';
import type { MonitorV2TrackResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    buildTrackTitle,
    formatTrackSinceFromDate,
    getConnectionIdsFromControlTest,
    getIsAnyConnectionMisconfigurated,
} from './track-card.helpers';

export class TrackCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    monitorEvents = new ObservedQuery(eventsControllerListEventsOptions);

    get isMonitorNotTestedStatus(): boolean {
        const { testDetails } = sharedMonitoringTestDetailsController;

        return !(
            testDetails?.checkResultStatus === 'READY' ||
            testDetails?.checkResultStatus === 'PREAUDIT'
        );
    }

    get hasPermissionIssues(): boolean {
        const regex = /.*is not authorized to perform*/;

        this.fetchMonitorEvents();

        if (isNil(this.monitorEvents.data?.data)) {
            return false;
        }

        const permissionErroredEvents = this.monitorEvents.data.data.filter(
            (event) =>
                !isEmpty(event.issues) &&
                event.issues.some(({ message }) =>
                    regex.test(message as string),
                ),
        );

        return !isEmpty(permissionErroredEvents);
    }

    get errorMessage(): string {
        const { testDetails } = sharedMonitoringTestDetailsController;

        const { allConfiguredConnections } = sharedConnectionsController;

        if (
            this.hasPermissionIssues &&
            (testDetails?.source === 'DRATA' ||
                testDetails?.source === 'CUSTOM')
        ) {
            return 'Fix permission error';
        }

        const connectionIds = getConnectionIdsFromControlTest(testDetails);
        const connectionsForControlTest = chain(allConfiguredConnections)
            .flatMap()
            .filter(
                (connection) =>
                    connection.id !== undefined &&
                    connectionIds.includes(connection.id),
            )
            .uniqWith(isEqual)
            .value();

        const hasConnectionError = getIsAnyConnectionMisconfigurated(
            connectionsForControlTest,
        );

        if (hasConnectionError) {
            return 'Fix connection error';
        }

        return 'Fix test error';
    }

    get modeledTrackData(): MonitorV2TrackResponseDto {
        if (this.isMonitorNotTestedStatus) {
            const { trackData } = activeTrackCardController;

            return {
                status: mapStatusToTrackStatus(
                    trackData?.status as MonitorTrackStatus,
                ) as MonitorV2TrackResponseDto['status'],
                consecutiveDays: trackData?.consecutiveDays || 0,
                startDateWithStatus:
                    trackData?.startDateWithStatus?.toString() || null,
            };
        }

        return {
            status: mapStatusToTrackStatus(
                'READY',
            ) as MonitorV2TrackResponseDto['status'],
            consecutiveDays: 0,
            startDateWithStatus: null,
        };
    }

    get title(): string {
        const { ticketingConnectionWithWriteAccess } =
            sharedConnectionsController;
        const { testDetails } = sharedMonitoringTestDetailsController;
        const checkResultStatus = testDetails?.checkResultStatus;

        return buildTrackTitle({
            hasTicketingConnection: Boolean(ticketingConnectionWithWriteAccess),
            trackStatus: checkResultStatus,
            errorTitle: this.errorMessage,
        });
    }

    get trackCardStatusMessage(): string {
        const { checkResultStatus } = sharedMonitoringTestDetailsController;

        switch (checkResultStatus) {
            case 'PASSED': {
                return 'Days passed';
            }
            case 'FAILED': {
                return 'Days failed';
            }
            case 'ERROR': {
                return 'Days erroring';
            }
            case 'READY':
            case 'PREAUDIT': {
                return 'No test results';
            }
            default: {
                return 'No test results';
            }
        }
    }

    get trackCardColorScheme(): ColorScheme {
        const { checkResultStatus } = sharedMonitoringTestDetailsController;

        switch (checkResultStatus) {
            case 'PASSED': {
                return 'success';
            }
            case 'FAILED': {
                return 'critical';
            }
            case 'ERROR': {
                return 'warning';
            }
            case 'READY':
            case 'PREAUDIT': {
                return 'neutral';
            }
            default: {
                return 'neutral';
            }
        }
    }

    get sinceDateText(): string {
        return formatTrackSinceFromDate(
            this.modeledTrackData.startDateWithStatus,
        );
    }

    get trackCardDays(): number | string {
        return this.modeledTrackData.consecutiveDays > 0
            ? this.modeledTrackData.consecutiveDays
            : '-';
    }

    fetchMonitorEvents(): void {
        const { testDetails } = sharedMonitoringTestDetailsController;

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                this.monitorEvents.load({
                    query: {
                        sortDir: 'DESC',
                        workspaceId: currentWorkspace?.id ?? 1,
                        source: 'AUTOPILOT',
                        testId: testDetails?.testId,
                        category: 'AUTOPILOT',
                        mostRecent: true,
                        paginated: true,
                    },
                });
            },
        );
    }
}
