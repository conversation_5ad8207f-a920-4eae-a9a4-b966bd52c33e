import type { ClientLoader } from '@app/types';
import { sharedCompaniesHumanResourcesController } from '@controllers/companies';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedSettingsAutomatedOffboardingController } from '@controllers/settings';
import { action } from '@globals/mobx';
import { SettingsPersonnelComplianceHumanResourcesView } from '@views/settings-personnel-compliance-human-resources';

export const clientLoader = action((): ClientLoader => {
    sharedCompaniesHumanResourcesController.loadCompaniesHumanResources();
    sharedSettingsAutomatedOffboardingController.loadAutomatedOffboarding();
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return null;
});

export const SettingsEmployeeComplianceHumanResourcesPage =
    (): React.JSX.Element => {
        return (
            <SettingsPersonnelComplianceHumanResourcesView
                data-testid="SettingsEmployeeComplianceHumanResourcesPage"
                data-id="GXZ_rNed"
            />
        );
    };

export default SettingsEmployeeComplianceHumanResourcesPage;
