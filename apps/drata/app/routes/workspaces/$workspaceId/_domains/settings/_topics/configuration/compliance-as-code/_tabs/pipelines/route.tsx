import type { MetaFunction } from '@remix-run/node';
import { SettingsComplianceAsCodePipelinesView } from '@views/settings-compliance-as-code-pipelines';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Compliance as Code' }];
};

export const SettingsComplianceAsCodePipelines = (): React.JSX.Element => {
    return (
        <SettingsComplianceAsCodePipelinesView
            data-testid="SettingsComplianceAsCodePipelines"
            data-id="ZtYoBL3D"
        />
    );
};

export default SettingsComplianceAsCodePipelines;
