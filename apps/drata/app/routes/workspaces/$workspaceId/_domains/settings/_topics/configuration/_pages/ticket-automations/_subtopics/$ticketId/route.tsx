import type { ClientLoader } from '@app/types';
import { sharedTicketAutomationDetailsController } from '@controllers/ticket-automation-details';
import { action } from '@globals/mobx';
import { TicketAutomationDetailsPageHeaderModel } from '@models/ticket-automation-details';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { SettingsTicketAutomationEditView } from '@views/settings-ticket-automation-edit';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Edit Ticket Rule' }];
};
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        if (!params.ticketId) {
            throw new Error('ticket ID is required');
        }

        sharedTicketAutomationDetailsController.loadTicketAutomationDetails(
            Number(params.ticketId),
        );

        return {
            pageHeader: new TicketAutomationDetailsPageHeaderModel(),
        };
    },
);

export const SettingsTicketAutomationEdit = (): React.JSX.Element => {
    return (
        <SettingsTicketAutomationEditView
            data-testid="SettingsTicketAutomationEdit"
            data-id="793UcUN6"
        />
    );
};

export default SettingsTicketAutomationEdit;
