import type { <PERSON>lientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { SettingsWorkspacesCreateView } from '@views/settings-workspaces-create';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Create Workspace' }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                isCentered: true,
                pageId: 'settings-workspaces-create',
                title: 'Create Workspaces',
                backLink: (
                    <AppLink href={parentHref} label="Back to Workspaces" />
                ),
            },
        };
    },
);

const CreateWorkspace = (): React.JSX.Element => {
    return (
        <SettingsWorkspacesCreateView
            data-testid="CreateWorkspace"
            data-id="V_Wemmt9"
        />
    );
};

export default CreateWorkspace;
