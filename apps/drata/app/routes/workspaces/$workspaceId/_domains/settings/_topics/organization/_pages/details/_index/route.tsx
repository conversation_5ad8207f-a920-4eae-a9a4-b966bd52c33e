import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Organization Info' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            isCentered: true,
            pageId: 'settings-organization-details-page-id',
            title: 'Organization Details',
        },
    };
});

export const SettingsOrganizationDetailsInfoPage = (): React.JSX.Element => {
    return (
        <div
            data-testid="SettingsOrganizationDetailsInfoPage"
            data-id="MzSGrZyJ"
        >
            OrganizationInfo Index
        </div>
    );
};

export default SettingsOrganizationDetailsInfoPage;
