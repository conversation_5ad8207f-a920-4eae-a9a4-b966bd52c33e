import type { <PERSON>lientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { SettingsApiKeysCreateView } from '@views/settings-api-keys-create';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Create API Key' }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                isCentered: true,
                pageId: 'settings-api-keys-create',
                title: 'Create API Key',
                backLink: (
                    <AppLink href={parentHref} label="Back to API Keys" />
                ),
            },
        };
    },
);

export const SettingsApiKeysCreatePage = (): React.JSX.Element => {
    return (
        <SettingsApiKeysCreateView
            data-testid="SettingsApiKeysCreatePage"
            data-id="977zILiV"
        />
    );
};

export default SettingsApiKeysCreatePage;
