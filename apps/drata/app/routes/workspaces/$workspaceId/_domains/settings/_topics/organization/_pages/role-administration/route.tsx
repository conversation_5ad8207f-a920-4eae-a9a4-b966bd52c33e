import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Role Administration' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-role-administration',
            title: 'Role Administration',
        },

        tabs: [
            {
                id: 'settings.organization.role-administration.users',
                topicPath: 'settings/organization/role-administration/users',
                label: 'Users',
            },
            {
                id: 'settings.organization.role-administration.guests',
                topicPath: 'settings/organization/role-administration/guests',
                label: 'Guests',
            },
        ],
    };
});

export const SettingsRoleAdministration = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsRoleAdministration"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsRoleAdministration;
