import type { ClientLoader } from '@app/types';
import { sharedEntitlementsQuotaController } from '@controllers/entitlements-quota';
import { sharedPlanAndUsageController } from '@controllers/plan-and-usage';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { action, reaction } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsPlanAndUsageView } from '@views/settings-plan-and-usage';

export const clientLoader = action((): ClientLoader => {
    sharedPlanAndUsageController.accountContractDetails.load();

    reaction(
        () => !sharedEntitlementFlagController.isLoading,
        () => {
            sharedEntitlementsQuotaController.entitlementQuotas.load({
                // TODO: figure out how to handle workspaces once that's available ENG-67742
                query: {
                    entitlements:
                        sharedEntitlementFlagController.companyEntitlementsWithQuotas,
                },
            });
        },
    );

    return {
        pageHeader: {
            isCentered: true,
            pageId: 'settings-plan-and-usage',
            title: 'Plan and Usage',
        },
    };
});

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Plan and Usage' }];
};

export const SettingsPlanAndUsage = (): React.JSX.Element => {
    return (
        <SettingsPlanAndUsageView
            data-testid="SettingsPlanAndUsage"
            data-id="ogvxbs1c"
        />
    );
};

export default SettingsPlanAndUsage;
