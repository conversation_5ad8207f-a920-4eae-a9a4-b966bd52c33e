import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { SettingsFieldsAndFormulasFormulaCreateView } from '@views/settings-fields-and-formulas-formula-create';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Create Formula' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            isCentered: true,
            pageId: 'settings-fields-and-formulas-create',
            title: 'Create Formula',
            backLink: (
                <AppLink
                    href="/settings/fields-and-formulas/formulas"
                    label="Back to Formulas"
                />
            ),
        },
    };
});

export const SettingsFieldsAndFormulasFields = (): React.JSX.Element => {
    return (
        <SettingsFieldsAndFormulasFormulaCreateView
            data-testid="SettingsFieldsAndFormulasFields"
            data-id="55GZyuNu"
        />
    );
};

export default SettingsFieldsAndFormulasFields;
