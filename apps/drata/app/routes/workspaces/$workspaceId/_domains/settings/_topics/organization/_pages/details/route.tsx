import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Organization Info' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            isCentered: true,
            pageId: 'settings-organization-details-page-id',
            title: 'Organization Details',
        },
        tabs: [
            {
                id: 'nav.settings.organization.details.information',
                topicPath: 'settings/organization/details/information',
                label: 'Org Info',
            },
            {
                id: 'nav.settings.organization.details.key-personnel',
                topicPath: 'settings/organization/details/key-personnel',
                label: 'Key Personnel',
            },
        ],
    };
});

export const SettingsOrganizationDetailsInfoPage = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsOrganizationDetailsInfoPage"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsOrganizationDetailsInfoPage;
