export const SettingsFieldsAndFormulasFields = (): React.JSX.Element => {
    return (
        <div data-testid="SettingsFieldsAndFormulasFields" data-id="rvmvJTut">
            <h1>SettingsFieldsAndFormulasFormulas Index </h1>

            <p>
                This seems like it should be a standard view state as a
                non-form, before hitting &quot;edit&quot;.
            </p>
            <p>
                Hitting &quot;edit&quot; should take you to the edit page, which
                is a form.
            </p>
        </div>
    );
};

export default SettingsFieldsAndFormulasFields;
