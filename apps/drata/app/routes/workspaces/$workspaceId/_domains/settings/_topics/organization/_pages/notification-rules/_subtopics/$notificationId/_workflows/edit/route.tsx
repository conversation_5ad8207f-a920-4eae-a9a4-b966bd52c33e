import type { MetaFunction } from '@remix-run/node';
import { SettingsOrganizationalNotificationsEditView } from '@views/settings-organizational-notifications-edit';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Edit Notification Rule' }];
};

const SettingsOrganizationalNotificationsEditPage = (): React.JSX.Element => {
    return (
        <SettingsOrganizationalNotificationsEditView
            data-testid="SettingsOrganizationalNotificationsEditPage"
            data-id="ZC8J21EJ"
        />
    );
};

export default SettingsOrganizationalNotificationsEditPage;
