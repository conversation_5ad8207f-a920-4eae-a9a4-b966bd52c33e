import type { ClientLoader } from '@app/types';
import { sharedUserDetailsController } from '@controllers/user-details';
import { sharedUserRisksController } from '@controllers/user-risks';
import { action, observer } from '@globals/mobx';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { SettingsRoleAdministrationUserDetailsView } from '@views/settings-role-administration-user-details';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Role Administration' }];
};

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { userId } = params;

        if (!userId) {
            throw new Error('User ID is required');
        }

        sharedUserDetailsController.getUserOwnedControls(Number(userId));
        sharedUserDetailsController.getUserOwnedPolicies(Number(userId));
        sharedUserDetailsController.getUserOwnedVendors(Number(userId));
        sharedUserDetailsController.getUserDetails(Number(userId));
        sharedUserRisksController.getUserRisks(Number(userId));

        return {
            tabs: [],
        };
    },
);

export const SettingsRoleAdministrationUserDetails = observer(
    (): React.JSX.Element => {
        return (
            <SettingsRoleAdministrationUserDetailsView
                data-testid="SettingsRoleAdministrationUserDetails"
                data-id="2odTTX0c"
            />
        );
    },
);

export default SettingsRoleAdministrationUserDetails;
