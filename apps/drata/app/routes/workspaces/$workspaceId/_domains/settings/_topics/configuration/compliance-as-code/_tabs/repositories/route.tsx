import type { MetaFunction } from '@remix-run/node';
import { SettingsComplianceAsCodeRepositoriesView } from '@views/settings-compliance-as-code-repositories';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Compliance as Code' }];
};

export const SettingsComplianceAsCodeRepositories = (): React.JSX.Element => {
    return (
        <SettingsComplianceAsCodeRepositoriesView
            data-testid="SettingsComplianceAsCodeRepositories"
            data-id="CSGPzFX0"
        />
    );
};

export default SettingsComplianceAsCodeRepositories;
