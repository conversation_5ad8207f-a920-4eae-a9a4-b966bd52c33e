import type { <PERSON>lientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Settings',
        },

        topicsNav: {
            id: 'nav.settings',
            title: 'Settings',
            domainsOrder: [
                'settings.personal',
                'settings.organization',
                'settings.configuration',
            ],
            domains: {
                'settings.personal': {
                    label: 'Personal',
                    hideLabel: false,
                    topicsOrder: [
                        'settings.personal.language',
                        'settings.personal.profile',
                        'settings.personal.notifications',
                    ],
                    topics: {
                        'settings.personal.language': {
                            id: 'settings.personal.language',
                            topicPath: 'settings/personal/language',
                            label: 'Language',
                        },
                        'settings.personal.profile': {
                            id: 'settings.personal.profile',
                            topicPath: 'settings/personal/profile',
                            label: 'Profile',
                        },
                        'settings.personal.notifications': {
                            id: 'settings.personal.notifications',
                            topicPath: 'settings/personal/notifications',
                            label: 'Notifications',
                        },
                    },
                },

                'settings.organization': {
                    label: 'Organization',
                    hideLabel: false,
                    topicsOrder: [
                        'settings.organization.language',
                        'settings.organization.details',
                        'settings.organization.notification-rules',
                        'settings.organization.personnel-compliance',
                        'settings.organization.plan-and-usage',
                        'settings.organization.role-administration',
                        'settings.organization.workspaces',
                    ],
                    topics: {
                        'settings.organization.language': {
                            id: 'settings.organization.language',
                            topicPath: 'settings/organization/language',
                            label: 'Language',
                        },
                        'settings.organization.details': {
                            id: 'settings.organization.details',
                            topicPath: 'settings/organization/details',
                            label: 'Organization details',
                        },
                        'settings.organization.notification-rules': {
                            id: 'settings.organization.notification-rules',
                            topicPath:
                                'settings/organization/notification-rules',
                            label: 'Notification rules',
                        },
                        'settings.organization.personnel-compliance': {
                            id: 'settings.organization.personnel-compliance',
                            topicPath:
                                'settings/organization/personnel-compliance',
                            label: 'Personnel compliance',
                        },
                        'settings.organization.plan-and-usage': {
                            id: 'settings.organization.plan-and-usage',
                            topicPath: 'settings/organization/plan-and-usage',
                            label: 'Plan and usage',
                        },
                        'settings.organization.role-administration': {
                            id: 'settings.organization.role-administration',
                            topicPath:
                                'settings/organization/role-administration',
                            label: 'Role administration',
                        },
                        'settings.organization.workspaces': {
                            id: 'settings.organization.workspaces',
                            topicPath: 'settings/organization/workspaces',
                            label: 'Workspaces',
                        },
                    },
                },

                'settings.configuration': {
                    label: 'Configuration',
                    hideLabel: false,
                    topicsOrder: [
                        'settings.configuration.ai',
                        'settings.configuration.api-keys',
                        'settings.configuration.compliance-as-code',
                        'settings.configuration.fields-and-formulas',
                        'settings.configuration.ticket-automations',
                        'settings.configuration.workflows',
                    ],
                    topics: {
                        'settings.configuration.ai': {
                            id: 'settings.configuration.ai',
                            topicPath: 'settings/configuration/ai',
                            label: 'AI settings',
                        },
                        'settings.configuration.api-keys': {
                            id: 'settings.configuration.api-keys',
                            topicPath: 'settings/configuration/api-keys',
                            label: 'API keys',
                        },
                        'settings.configuration.compliance-as-code': {
                            id: 'settings.configuration.compliance-as-code',
                            topicPath:
                                'settings/configuration/compliance-as-code',
                            label: 'Compliance as Code',
                        },
                        'settings.configuration.fields-and-formulas': {
                            id: 'settings.configuration.fields-and-formulas',
                            topicPath:
                                'settings/configuration/fields-and-formulas',
                            label: 'Fields and formulas',
                        },
                        'settings.configuration.ticket-automations': {
                            id: 'settings.configuration.ticket-automations',
                            topicPath:
                                'settings/configuration/ticket-automations',
                            label: 'Ticket automation',
                        },
                        'settings.configuration.workflows': {
                            id: 'settings.configuration.workflows',
                            topicPath: 'settings/configuration/workflows',
                            label: 'Workflows',
                        },
                    },
                },
            },
        },
    };
});

const Settings = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Settings" data-id="ISzzDb1x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Settings;
