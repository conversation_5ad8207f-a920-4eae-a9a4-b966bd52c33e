import type { ClientLoader } from '@app/types';
import { sharedCompaniesSecurityController } from '@controllers/companies';
import { action } from '@globals/mobx';
import { SettingsPersonnelComplianceInternalSecurityView } from '@views/settings-personnel-compliance-internal-security';

export const clientLoader = action((): ClientLoader => {
    sharedCompaniesSecurityController.loadCompaniesSecurity();

    return null;
});

export const SettingsEmployeeCompliancePageInternalSecurity =
    (): React.JSX.Element => {
        return (
            <SettingsPersonnelComplianceInternalSecurityView
                data-testid="SettingsEmployeeCompliancePageInternalSecurity"
                data-id="GXZ_rNed"
            />
        );
    };

export default SettingsEmployeeCompliancePageInternalSecurity;
