import type { ClientLoader } from '@app/types';
import {
    sharedNotificationRuleChannelsController,
    sharedNotificationRuleController,
} from '@controllers/notification-rule';
import { action } from '@globals/mobx';
import { NotificationRuleHeaderModel } from '@models/notification-rule';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = ({ params }) => {
    return [
        { title: `Settings - Notification Rule: ${params.notificationId}` },
    ];
};

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { notificationId } = params;

        if (!notificationId) {
            throw new Error('Invalid Notification Rule ID');
        }

        sharedNotificationRuleController.loadNotificationRule(
            Number(notificationId),
        );
        sharedNotificationRuleChannelsController.loadNotificationRuleChannels(
            'SLACK',
        );

        return {
            pageHeader: new NotificationRuleHeaderModel(),
        };
    },
);

const SettingsOrganizationalNotificationsEditPage = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsOrganizationalNotificationsEditPage"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsOrganizationalNotificationsEditPage;
