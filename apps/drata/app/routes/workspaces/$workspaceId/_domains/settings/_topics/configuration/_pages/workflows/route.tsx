import type { ClientLoader } from '@app/types';
import { ActionStack, type Stack } from '@cosmos/components/action-stack';
import { dimensionSm } from '@cosmos/constants/tokens';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Workflows' }];
};

const workflowsStacks: Stack[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'settings-workflows-create-stack',
                typeProps: {
                    label: 'Create workflow',
                    colorScheme: 'primary',
                },
            },
        ],
        id: 'settings-workflows-create-action-stack',
    },
];

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-workflows',
            title: 'Workflows',
            actionStack: (
                <ActionStack
                    data-id="settings-workflows-action-stack"
                    gap={dimensionSm}
                    stacks={workflowsStacks}
                />
            ),
        },
        tabs: [
            {
                id: 'settings.configuration.workflows.active',
                topicPath: 'settings/configuration/workflows/active',
                label: 'Active',
            },
            {
                id: 'settings.configuration.workflows.archived',
                topicPath: 'settings/configuration/workflows/archived',
                label: 'Archived',
            },
        ],
    };
});

const Workflows = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Workflows" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Workflows;
