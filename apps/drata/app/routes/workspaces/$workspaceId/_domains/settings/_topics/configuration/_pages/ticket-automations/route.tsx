import type { ClientLoader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Ticket Automation' }];
};

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedTicketAutomationController.loadTicketAutomation({
        pagination: {
            page: 1,
            pageSize: DEFAULT_PAGE_SIZE,
        },
    } as FetchDataResponseParams);
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: {
            pageId: 'settings-ticket-automation',
            title: 'Ticket Automation',
        },
    };
});

export const SettingsTicketAutomation = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsTicketAutomation"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsTicketAutomation;
