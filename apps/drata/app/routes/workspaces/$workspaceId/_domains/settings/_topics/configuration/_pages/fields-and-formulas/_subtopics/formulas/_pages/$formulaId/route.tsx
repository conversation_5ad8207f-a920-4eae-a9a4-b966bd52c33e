import type { ClientLoader } from '@app/types';
import { sharedFieldsAndFormulasFormulaDetailsController } from '@controllers/fields-and-formulas-formula-details';
import { action } from '@globals/mobx';
import { FieldsAndFormulasFormulaPageHeaderModel } from '@models/fields-and-formulas-formula';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { SettingsFieldsAndFormulasFormulaEditView } from '@views/settings-fields-and-formulas-formula-edit';

export const meta: MetaFunction = ({ params }) => {
    return [{ title: `Settings - Formula: ${params.formulaId}` }];
};
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        if (!params.formulaId) {
            throw new Error('formula ID is required');
        }

        sharedFieldsAndFormulasFormulaDetailsController.formulaDetails.load({
            path: { customFieldId: parseInt(params.formulaId) },
        });

        return {
            pageHeader: new FieldsAndFormulasFormulaPageHeaderModel(),
            tabs: [],
        };
    },
);

export const SettingsFieldsAndFormulasFields = (): React.JSX.Element => {
    return (
        <SettingsFieldsAndFormulasFormulaEditView
            data-testid="SettingsFieldsAndFormulasFields"
            data-id="KsJNr2UU"
        />
    );
};

export default SettingsFieldsAndFormulasFields;
