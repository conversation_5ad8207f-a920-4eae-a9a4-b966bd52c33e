import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Compliance as Code' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-compliance-as-code',
            title: 'Compliance as Code',
        },

        tabs: [
            {
                id: 'settings.configuration.compliance-as-code.repositories',
                topicPath:
                    'settings/configuration/compliance-as-code/repositories',
                label: 'Repositories',
            },
            {
                id: 'settings.configuration.compliance-as-code.pipelines',
                topicPath:
                    'settings/configuration/compliance-as-code/pipelines',
                label: 'Pipelines',
            },
        ],
    };
});

export const SettingsComplianceAsCode = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsComplianceAsCode"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsComplianceAsCode;
