import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Fields and Formulas' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-fields-and-formulas',
            title: 'Fields and Formulas',
        },

        tabs: [
            {
                id: 'settings.configuration.fields-and-formulas.fields',
                topicPath: 'settings/configuration/fields-and-formulas/fields',
                label: 'Fields',
            },
            {
                id: 'settings.configuration.fields-and-formulas.formulas',
                topicPath:
                    'settings/configuration/fields-and-formulas/formulas',
                label: 'Formulas',
            },
        ],
    };
});

export const SettingsFieldsAndFormulasFields = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsFieldsAndFormulasFields"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsFieldsAndFormulasFields;
