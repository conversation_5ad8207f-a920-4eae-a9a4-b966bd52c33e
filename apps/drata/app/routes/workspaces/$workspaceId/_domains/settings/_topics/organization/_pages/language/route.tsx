import type { ClientLoader } from '@app/types';
import { Metadata } from '@cosmos/components/metadata';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsLanguageView } from '@views/settings-language';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Default Language' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            isCentered: true,
            title: 'Default Language',
            slot: (
                <Metadata colorScheme="education" label="Beta" type="status" />
            ),
        },
    };
});

export const SettingsLanguage = (): React.JSX.Element => {
    return (
        <SettingsLanguageView
            data-testid="SettingsLanguage"
            data-id="nY4-Xp93"
        />
    );
};

export default SettingsLanguage;
