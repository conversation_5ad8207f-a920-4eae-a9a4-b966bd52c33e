import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const SettingsFieldsAndFormulasFormulas = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsFieldsAndFormulasFormulas"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsFieldsAndFormulasFormulas;
