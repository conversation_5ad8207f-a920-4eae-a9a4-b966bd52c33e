import { noop } from 'lodash-es';
import { ActionStack, type Stack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { dimensionLg } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';

const BASE_ID = 'workflows-details';

const STACKS: Stack[] = [
    {
        actions: [
            {
                actionType: 'dropdown',
                id: `${BASE_ID}-more-options-stack`,
                typeProps: {
                    label: 'More options',
                    isIconOnly: true,
                    startIconName: 'HorizontalMenu',
                    colorScheme: 'primary',
                    level: 'tertiary',
                    'data-id': `${BASE_ID}-more-options-dropdown`,
                    items: [
                        {
                            id: `${BASE_ID}-dropdown-edit-draft`,
                            label: 'Edit draft',
                            onSelect: noop,
                        },
                        {
                            id: `${BASE_ID}-dropdown-view-version`,
                            label: 'View published version',
                            onSelect: noop,
                        },
                        {
                            id: `${BASE_ID}-dropdown-publish`,
                            label: 'Publish',
                            onSelect: noop,
                        },
                        {
                            id: `${BASE_ID}-dropdown-unpublish`,
                            label: 'Unpublish',
                            onSelect: noop,
                        },
                        {
                            id: `${BASE_ID}-dropdown-duplicate`,
                            label: 'Duplicate',
                            onSelect: noop,
                        },
                        {
                            id: `${BASE_ID}-dropdown-archive`,
                            label: 'Archive',
                            onSelect: noop,
                        },
                        {
                            id: `${BASE_ID}-dropdown-group`,
                            type: 'group',
                            items: [
                                {
                                    id: `${BASE_ID}-dropdown-delete`,
                                    label: 'Delete workflow',
                                    startIconName: 'Trash',
                                    onSelect: noop,
                                },
                            ],
                        },
                    ],
                },
            },
            {
                actionType: 'button',
                id: `${BASE_ID}-create-draft-stack`,
                typeProps: {
                    label: 'Create draft',
                    colorScheme: 'primary',
                    'data-id': `${BASE_ID}-create-draft-btn`,
                    onClick: noop,
                },
            },
        ],
        id: `${BASE_ID}-stack`,
    },
];

const KEY_VALUES: KeyValuePairProps[] = [
    {
        id: `${BASE_ID}-status-kvp`,
        'data-id': `${BASE_ID}-status-kvp`,
        label: 'Status',
        type: 'BADGE',
        value: {
            label: 'Published',
            type: 'status',
            colorScheme: 'success',
        },
    },
    {
        id: `${BASE_ID}-type-kvp`,
        'data-id': `${BASE_ID}-type-kvp`,
        label: 'Type',
        type: 'TEXT',
        iconName: 'Edit',
        value: '[TYPE]',
    },
    {
        id: `${BASE_ID}-created-by-kvp`,
        'data-id': `${BASE_ID}-created-by-kvp`,
        label: 'Created by',
        type: 'USER',
        value: {
            username: '[User name]',
            avatarProps: {
                fallbackText: 'CS',
                imgSrc: '',
                imgAlt: '',
            },
        },
    },
    {
        id: `${BASE_ID}-workspace-kvp`,
        'data-id': `${BASE_ID}-workspace-kvp`,
        label: 'Workspace',
        type: 'TAG',
        iconName: 'Edit',
        value: {
            label: '[Workspace name]',
            type: 'tag',
            colorScheme: 'neutral',
        },
    },
];

export const BREADCRUMBS: Breadcrumb[] = [
    {
        label: 'Workflows',
        pathname: '/settings/workflows',
    },
];

export const handle = {
    overrides: {
        pageHeader: {
            pageId: `${BASE_ID}-header`,
            title: '[Workflow name]',
            slot: <Metadata colorScheme="neutral" label="v1" type="tag" />,
            keyValuePairs: KEY_VALUES,
            breadcrumbs: BREADCRUMBS,
            actionStack: (
                <ActionStack
                    data-id={`${BASE_ID}-action-stack`}
                    gap={dimensionLg}
                    stacks={STACKS}
                />
            ),
        },
    },
};

const WorkflowsActive = (): React.JSX.Element => {
    return (
        <h1 data-testid="WorkflowsActive" data-id="yQwolgYi">
            WorkflowsActive cannot use Tabs component. Talk to chris about how
            to fix this
        </h1>
    );
};

export default WorkflowsActive;
