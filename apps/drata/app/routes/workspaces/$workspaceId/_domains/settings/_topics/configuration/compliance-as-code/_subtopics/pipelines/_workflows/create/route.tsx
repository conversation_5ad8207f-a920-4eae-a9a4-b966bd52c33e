import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsComplianceAsCodePipelinesCreateView } from '@views/settings-compliance-as-code-pipelines-create';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Compliance as Code - Create Pipeline' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-compliance-as-code-pipelines-create-pipeline',
            title: 'Create pipeline Key',
        },

        tabs: [],
    };
});

export const SettingsComplianceAsCodePipelinesCreatePipeline =
    (): React.JSX.Element => {
        return (
            <SettingsComplianceAsCodePipelinesCreateView
                data-testid="SettingsComplianceAsCodePipelinesCreatePipeline"
                data-id="sv8RKlOY"
            />
        );
    };

export default SettingsComplianceAsCodePipelinesCreatePipeline;
