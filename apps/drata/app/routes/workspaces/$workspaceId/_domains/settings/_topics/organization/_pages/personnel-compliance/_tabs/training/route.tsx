import type { ClientLoader } from '@app/types';
import { sharedCompaniesSecurityController } from '@controllers/companies';
import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedSettingsHipaaTrainingController,
    sharedSettingsNistaiTrainingController,
    sharedSettingsSecurityTrainingController,
} from '@controllers/settings';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsPersonnelComplianceTrainingView } from '@views/settings-personnel-compliance-training';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Training' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedCompaniesSecurityController.loadCompaniesSecurity();
    sharedSettingsSecurityTrainingController.loadSecurityTraining();
    sharedSettingsHipaaTrainingController.loadHipaaTraining();
    sharedSettingsNistaiTrainingController.loadNistaiTraining();
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return null;
});

export const SettingsEmployeeCompliancePageTraining = (): React.JSX.Element => {
    return (
        <SettingsPersonnelComplianceTrainingView
            data-testid="SettingsEmployeeCompliancePageTraining"
            data-id="GXZ_rNed"
        />
    );
};

export default SettingsEmployeeCompliancePageTraining;
