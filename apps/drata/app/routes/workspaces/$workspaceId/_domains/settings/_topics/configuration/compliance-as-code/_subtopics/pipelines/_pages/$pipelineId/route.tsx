import type { ClientLoader } from '@app/types';
import { sharedPipelineApiKeysController } from '@controllers/pipeline-api-keys';
import { action } from '@globals/mobx';
import { SettingsComplianceAsCodePipelineDetailsPageHeaderModel } from '@models/settings-compliance-as-code-pipeline-details-page-header';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { SettingsComplianceAsCodePipelinesDetailView } from '@views/settings-compliance-as-code-pipelines-detail-view';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Compliance as Code - Pipeline Key Details' }];
};
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { pipelineId } = params;

        if (!pipelineId) {
            throw new Error('Invalid pipeline key id');
        }

        const { loadPipelineApiKeyDetails } = sharedPipelineApiKeysController;

        loadPipelineApiKeyDetails(pipelineId);

        return {
            pageHeader:
                new SettingsComplianceAsCodePipelineDetailsPageHeaderModel(),
            tabs: [],
        };
    },
);

export const SettingsComplianceAsCodePipelinesDetails =
    (): React.JSX.Element => {
        return (
            <SettingsComplianceAsCodePipelinesDetailView
                data-id="MNSEn-RL"
                data-testid="SettingsComplianceAsCodePipelinesDetails"
            />
        );
    };

export default SettingsComplianceAsCodePipelinesDetails;
