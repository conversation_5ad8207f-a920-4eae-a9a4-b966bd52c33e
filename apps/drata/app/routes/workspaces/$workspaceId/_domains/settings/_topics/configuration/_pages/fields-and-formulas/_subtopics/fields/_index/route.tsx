import type { ClientLoader } from '@app/types';
import { sharedCustomFieldsController } from '@controllers/custom-fields';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsFieldsAndFormulasFieldsView } from '@views/settings-fields-and-formulas-fields';

/**
 * These values are defined here so we can have something to work with,
 * but at some point this has to be dynamically picked from the URL.
 */
const INITIAL_PAGE = 1;
const INITIAL_PAGE_SIZE = 10;

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Fields and Formulas' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedCustomFieldsController.customFields.load({
        query: {
            page: INITIAL_PAGE,
            limit: INITIAL_PAGE_SIZE,
            excludeTypes: ['FORMULA'],
        },
    });

    return null;
});

export const SettingsFieldsAndFormulasFields = (): React.JSX.Element => {
    return (
        <SettingsFieldsAndFormulasFieldsView
            data-testid="SettingsFieldsAndFormulasFields"
            data-id="QVd9wFn3"
        />
    );
};

export default SettingsFieldsAndFormulasFields;
