import type { <PERSON>lientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { SettingsTicketAutomationBasicView } from '@views/settings-ticket-automation-basic';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Create Ticket Rule' }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                isCentered: true,
                pageId: 'settings-ticket-automation-create',
                title: 'Create Ticket Rule',
                backLink: (
                    <AppLink
                        href={parentHref}
                        label="Back to Ticket Automation"
                    />
                ),
            },
        };
    },
);

export const SettingsTicketAutomationCreate = (): React.JSX.Element => {
    return (
        <SettingsTicketAutomationBasicView
            data-testid="SettingsTicketAutomationCreate"
            data-id="YeRMwDG7"
        />
    );
};

export default SettingsTicketAutomationCreate;
