import type { ClientLoader } from '@app/types';
import { sharedApiKeysDetailsController } from '@controllers/api-keys';
import { sharedApiKeysPermissionsController } from '@controllers/api-keys-permissions';
import { action } from '@globals/mobx';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { SettingsApiKeysDetailsView } from '@views/settings-api-keys-details';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - API Key Details' }];
};
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { apiKeyId } = params;

        if (!apiKeyId) {
            throw new Error('Invalid api key id');
        }

        sharedApiKeysDetailsController.getApiKeysDetails(apiKeyId);
        sharedApiKeysPermissionsController.getApiKeysPermissionList();

        return {
            pageHeader: {
                title: 'API Key Details',
            },
        };
    },
);

export const SettingsApiKeysDetailsPage = (): React.JSX.Element => {
    return (
        <SettingsApiKeysDetailsView
            data-testid="SettingsApiKeysDetailsPage"
            data-id="977zILiV"
        />
    );
};

export default SettingsApiKeysDetailsPage;
