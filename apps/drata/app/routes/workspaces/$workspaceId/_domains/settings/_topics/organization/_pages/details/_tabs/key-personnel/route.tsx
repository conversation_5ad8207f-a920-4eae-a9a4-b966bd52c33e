import type { ClientLoader } from '@app/types';
import { sharedOrganizationKeyPersonnelController } from '@controllers/companies';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsOrganizationDetailsKeyPersonnelView } from '@views/settings-organization-details-key-personnel';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Organization Key Personnel' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedOrganizationKeyPersonnelController.getOrganizationKeyPersonnel();

    return {
        pageHeader: {
            title: 'Organization Key Personnel',
        },
    };
});

export const SettingsOrganizationDetailsKeyPersonnelPage =
    (): React.JSX.Element => {
        return (
            <SettingsOrganizationDetailsKeyPersonnelView
                data-testid="SettingsOrganizationDetailsKeyPersonnelPage"
                data-id="1NYKbPXN"
            />
        );
    };

export default SettingsOrganizationDetailsKeyPersonnelPage;
