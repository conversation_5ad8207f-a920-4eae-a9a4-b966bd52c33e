import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { useLocation } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - API Keys' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-api-keys',
            title: 'API Keys',
        },
    };
});

export const SettingsApiKeysPage = (): React.JSX.Element => {
    const { pathname } = useLocation();

    return (
        <div data-testid="SettingsApiKeysPage" data-id="Ej3gzEir">
            IDK why this page is broken, SettingsApiKeysPage._index
            <div>
                TEMP topic nav
                <nav
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '4px',
                    }}
                >
                    <AppLink href={`${pathname}/create`} label="Create" />

                    <AppLink
                        href={`${pathname}/some-foo`}
                        label="Some api key id"
                    />
                </nav>
            </div>
        </div>
    );
};

export default SettingsApiKeysPage;
