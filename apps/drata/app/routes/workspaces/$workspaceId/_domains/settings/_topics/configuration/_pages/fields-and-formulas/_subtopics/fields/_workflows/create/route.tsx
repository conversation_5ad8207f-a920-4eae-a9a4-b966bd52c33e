import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { SettingsFieldsAndFormulasFieldsCreateView } from '@views/settings-fields-and-formulas-fields-create';

export const meta: MetaFunction = () => {
    return [{ title: 'Fields and Formulas - Create Field' }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                isCentered: true,
                title: 'Create field',
                pageId: 'settings-fields-and-formulas-fields-create',
                backLink: (
                    <AppLink
                        href={parentHref}
                        label="Back to Fields and Formulas"
                    />
                ),
            },

            tabs: [],
        };
    },
);

export const SettingsFieldsAndFormulasFormulas = (): React.JSX.Element => {
    return (
        <SettingsFieldsAndFormulasFieldsCreateView
            data-testid="SettingsFieldsAndFormulasFormulas"
            data-id="rcSdLctk"
        />
    );
};

export default SettingsFieldsAndFormulasFormulas;
