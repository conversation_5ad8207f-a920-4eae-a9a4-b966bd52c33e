import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Personnel Compliance' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-personnel-compliance',
            title: 'Personnel Compliance',
        },

        tabs: [
            {
                id: 'settings.organization.personnel-compliance.internal-security',
                topicPath:
                    'settings/organization/personnel-compliance/internal-security',
                label: 'Internal Security',
            },
            {
                id: 'settings.organization.personnel-compliance.training',
                topicPath:
                    'settings/organization/personnel-compliance/training',
                label: 'Training',
            },
            {
                id: 'settings.organization.personnel-compliance.human-resources',
                topicPath:
                    'settings/organization/personnel-compliance/human-resources',
                label: 'Human Resources',
            },
        ],
    };
});

export const SettingsPersonnelCompliancePage = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsPersonnelCompliancePage"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsPersonnelCompliancePage;
