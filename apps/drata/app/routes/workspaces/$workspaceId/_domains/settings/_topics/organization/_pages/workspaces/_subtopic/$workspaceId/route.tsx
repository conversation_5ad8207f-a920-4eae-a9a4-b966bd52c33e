import type { <PERSON>lient<PERSON>oader } from '@app/types';
import { sharedWorkspaceDetailsController } from '@controllers/workspace-details';
import { sharedWorkspacesUserRolesController } from '@controllers/workspaces-user-roles';
import { action } from '@globals/mobx';
import { WorkspaceDetailsPageHeaderModel } from '@models/workspace-details';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = ({ params }) => {
    return [{ title: `Settings - Workspace: ${params.workspaceId}` }];
};
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        if (!params.workspaceId) {
            throw new Error('workspace ID is required');
        }

        const workspaceId = Number(params.workspaceId);

        sharedWorkspaceDetailsController.workspace.load({
            path: { id: workspaceId },
        });

        sharedWorkspacesUserRolesController.workspaceUserRoles.load({
            path: { xProductId: workspaceId },
        });

        return {
            pageHeader: new WorkspaceDetailsPageHeaderModel(),
        };
    },
);

const SettingsWorkspacesEdit = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsWorkspacesEdit"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsWorkspacesEdit;
