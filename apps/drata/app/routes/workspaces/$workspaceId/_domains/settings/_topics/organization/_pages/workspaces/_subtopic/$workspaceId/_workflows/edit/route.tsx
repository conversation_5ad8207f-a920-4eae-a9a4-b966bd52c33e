import type { <PERSON><PERSON>Loader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { SettingsWorkspacesEditView } from '@views/settings-workspaces-edit';

export const meta: MetaFunction = ({ params }) => {
    return [{ title: `Settings - Edit Workspace: ${params.workspaceId}` }];
};

export const clientLoader = action(
    ({ request, params }: ClientLoaderFunctionArgs): ClientLoader => {
        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                isCentered: true,
                pageId: 'settings-notification-rules-edit',
                title: `Edit Notification Rule - ${params.workspaceId}`,
                backLink: (
                    <AppLink
                        href={parentHref}
                        label="Back to Notification Rules"
                    />
                ),
            },
        };
    },
);

const SettingsWorkspacesEdit = (): React.JSX.Element => {
    return (
        <SettingsWorkspacesEditView
            data-testid="SettingsWorkspacesEdit"
            data-id="_o1DdCl_"
        />
    );
};

export default SettingsWorkspacesEdit;
