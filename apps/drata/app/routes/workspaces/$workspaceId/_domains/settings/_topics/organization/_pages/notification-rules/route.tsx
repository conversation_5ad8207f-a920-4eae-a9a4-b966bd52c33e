import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Notification Rules' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            pageId: 'settings-notification-rules',
            title: 'Notification Rules',
        },
    };
});

const SettingsOrganizationalNotificationsPage = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsOrganizationalNotificationsPage"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsOrganizationalNotificationsPage;
