import type { ClientLoader } from '@app/types';
import {
    sharedRiskInsightsController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { RiskInsightsView } from '@views/risk-insights';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk Insights' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedRiskInsightsController.load();
    sharedRiskSettingsController.load();

    return {
        pageHeader: {
            title: 'Risk Insights',
        },
    };
});

const RiskInsights = (): React.JSX.Element => {
    return <RiskInsightsView data-testid="RiskInsights" data-id="D61xoR_R" />;
};

export default RiskInsights;
