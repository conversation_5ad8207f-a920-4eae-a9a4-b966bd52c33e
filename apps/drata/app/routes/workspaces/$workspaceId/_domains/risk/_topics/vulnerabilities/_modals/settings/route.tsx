import { RiskVulnerabilitiesModalSettingsComponent } from '@components/risk-vulnerabilities';
import type { MetaFunction } from '@remix-run/node';

export const meta: MetaFunction = () => [
    { title: 'Vulnerabilities Settings Modal' },
];

const RiskVulnerabilitiesSettings = (): React.JSX.Element => {
    return (
        <RiskVulnerabilitiesModalSettingsComponent
            data-testid="RiskVulnerabilitiesSettings"
            data-id="s_HiG1i_"
        />
    );
};

export default RiskVulnerabilitiesSettings;
