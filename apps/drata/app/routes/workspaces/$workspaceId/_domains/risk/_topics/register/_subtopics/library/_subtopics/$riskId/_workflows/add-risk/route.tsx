import { RouteLandmark } from '@ui/layout-landmarks';

const RiskLibraryWorkflowAddRisk = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskLibraryWorkflowAddRisk"
            data-id="dataid"
        >
            <h1>RiskLibraryWorkflowAddRisk</h1>
            <h2>TODO: This needs some help. Talk to chris</h2>
        </RouteLandmark>
    );
};

export default RiskLibraryWorkflowAddRisk;
