import type { MetaFunction } from '@remix-run/node';
import { RiskRegisterLibraryView } from '@views/risk-register-library';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk Register Library' }];
};

const RiskRegisterLibrary = (): React.JSX.Element => {
    return (
        <RiskRegisterLibraryView
            data-testid="RiskRegisterLibrary"
            data-id="dataid"
        />
    );
};

export default RiskRegisterLibrary;
