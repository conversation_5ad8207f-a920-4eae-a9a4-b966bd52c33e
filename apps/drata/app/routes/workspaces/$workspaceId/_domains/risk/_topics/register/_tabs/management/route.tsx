import type { MetaFunction } from '@remix-run/node';
import { RiskRegisterManagementView } from '@views/risk-register-management';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk Register Management' }];
};

const RiskRegisterManagement = (): React.JSX.Element => {
    return (
        <RiskRegisterManagementView
            data-testid="RiskRegisterManagement"
            data-id="r7cJLl0b"
        />
    );
};

export default RiskRegisterManagement;
