import { RiskVulnerabilitiesModalResyncComponent } from '@components/risk-vulnerabilities';
import type { MetaFunction } from '@remix-run/node';

export const meta: MetaFunction = () => [
    { title: 'Vulnerabilities Resync Modal' },
];

const RiskVulnerabilitiesResync = (): React.JSX.Element => {
    return (
        <RiskVulnerabilitiesModalResyncComponent
            data-testid="RiskVulnerabilitiesResync"
            data-id="EGTZUv9o"
        />
    );
};

export default RiskVulnerabilitiesResync;
