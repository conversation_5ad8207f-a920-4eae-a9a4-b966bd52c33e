import type { ClientLoader } from '@app/types';
import { sharedRiskVulnerabilitiesController } from '@controllers/risk-vulnerabilities';
import { action, observer } from '@globals/mobx';
import { RiskVulnerabilitiesPageHeaderModel } from '@models/risk-vulnerabilities';
import type { MetaFunction } from '@remix-run/node';
import { RiskVulnerabilitiesView } from '@views/risk-vulnerabilities';

export const meta: MetaFunction = () => [{ title: 'Risk Vulnerabilities' }];

export const clientLoader = action((): ClientLoader => {
    sharedRiskVulnerabilitiesController.loadAll();

    return {
        pageHeader: new RiskVulnerabilitiesPageHeaderModel(),
    };
});

const RiskVulnerabilities = observer((): React.JSX.Element => {
    return (
        <RiskVulnerabilitiesView
            data-testid="RiskVulnerabilities"
            data-id="TTWxTKs1"
        />
    );
});

export default RiskVulnerabilities;
