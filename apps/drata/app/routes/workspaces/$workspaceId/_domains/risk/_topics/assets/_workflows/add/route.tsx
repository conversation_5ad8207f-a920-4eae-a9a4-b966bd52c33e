import type { ClientLoader } from '@app/types';
import { sharedUsersController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AddAssetView } from '@views/add-asset';

export const meta: MetaFunction = () => [{ title: t`Add an asset` }];

export const clientLoader = action((): ClientLoader => {
    sharedUsersController.usersList.load();

    return {
        pageHeader: {
            title: t`Add an asset`,
            pageId: 'add-asset-page-header',
        },
    };
});

const AddRiskAsset = (): React.JSX.Element => {
    return <AddAssetView data-testid="AddRiskAsset" data-id="vJtg_bBY" />;
};

export default AddRiskAsset;
