import type { ClientLoader } from '@app/types';
import { sharedRiskVulnerabilitiesDetailsController } from '@controllers/risk-vulnerabilities';
import { action } from '@globals/mobx';
import { RiskVulnerabilitiesDetailsHeaderModel } from '@models/risk-vulnerabilities';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { RouteLandmark } from '@ui/layout-landmarks';
import { RiskVulnerabilitiesDetailsView } from '@views/risk-vulnerabilities-details';

export const meta: MetaFunction = () => [{ title: 'Vulnerability Details' }];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { id, connectionId } = params;

        if (!id || !connectionId) {
            throw new Error('Invalid vulnerability parameters');
        }

        sharedRiskVulnerabilitiesDetailsController.loadVulnerabilityDetails(
            connectionId,
            id,
        );

        return {
            pageHeader: new RiskVulnerabilitiesDetailsHeaderModel(),
        };
    },
);

const RiskVulnerabilityDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskVulnerabilityDetails"
            data-id="BQ3lHUg5"
        >
            <h1>TODO: This needs some help. Talk to chris</h1>
            <RiskVulnerabilitiesDetailsView
                data-id="KAJiZ-6x"
                data-testid="RiskVulnerabilityDetails"
            />
        </RouteLandmark>
    );
};

export default RiskVulnerabilityDetails;
