import { RiskVulnerabilitiesModalConnectionComponent } from '@components/risk-vulnerabilities';
import type { MetaFunction } from '@remix-run/node';

export const meta: MetaFunction = () => [
    { title: 'Vulnerabilities Connection Modal' },
];

const RiskVulnerabilitiesConnection = (): React.JSX.Element => {
    return (
        <RiskVulnerabilitiesModalConnectionComponent
            data-testid="RiskVulnerabilitiesConnection"
            data-id="PYK4cSPu"
        />
    );
};

export default RiskVulnerabilitiesConnection;
