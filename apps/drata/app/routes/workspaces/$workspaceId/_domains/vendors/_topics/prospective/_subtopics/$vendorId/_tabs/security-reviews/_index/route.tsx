import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewsView } from '@views/vendors-profile-security-reviews';

export const meta: MetaFunction = () => [
    { title: 'Vendors Prospective Security Reviews' },
];

const VendorsProspectiveSecurityReviews = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewsView
            data-testid="VendorsProspectiveSecurityReviews"
            data-id="yibkgpMp"
        />
    );
};

export default VendorsProspectiveSecurityReviews;
