import type { MetaFunction } from '@remix-run/node';
import { VendorsProspectiveSettingsView } from '@views/vendors-prospective-settings';

export const meta: MetaFunction = () => [{ title: 'Vendors Settings' }];

export const handle = {
    overrides: {
        pageHeader: {
            title: 'TEMP Settings',
            pageId: 'vendors-temp-settings-page',
        },
    },
};

const VendorsSettings = (): React.JSX.Element => {
    return (
        <VendorsProspectiveSettingsView
            data-testid="VendorsSettings"
            data-id="ZeT9i8c-"
        />
    );
};

export default VendorsSettings;
