import type { MetaFunction } from '@remix-run/node';
import { VendorsQuestionnairesView } from '@views/vendors-questionnaires';

export const meta: MetaFunction = () => [{ title: 'Vendors Questionnaires' }];

const VendorsQuestionnaires = (): React.JSX.Element => {
    return (
        <VendorsQuestionnairesView
            data-testid="VendorsQuestionnaires"
            data-id="Ex8ZidjT"
        />
    );
};

export default VendorsQuestionnaires;
