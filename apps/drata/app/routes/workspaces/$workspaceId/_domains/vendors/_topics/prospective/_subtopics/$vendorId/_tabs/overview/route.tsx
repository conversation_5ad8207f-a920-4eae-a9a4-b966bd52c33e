import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileOverviewView } from '@views/vendors-profile-overview';

export const meta: MetaFunction = () => [
    { title: t`Vendors Prospective Overview` },
];

const VendorsProspectiveOverview = (): React.JSX.Element => {
    return (
        <VendorsProfileOverviewView
            data-testid="VendorsProspectiveOverview"
            data-id="CwvNeGR3"
        />
    );
};

export default VendorsProspectiveOverview;
