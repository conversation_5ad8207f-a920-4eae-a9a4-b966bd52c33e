import type { ClientLoader } from '@app/types';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
    sharedVendorsSecurityReviewObservationsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { VendorsSecurityReviewCompletedPageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsSecurityReviewCompletedView } from '@views/vendors-security-review-completed';

export const meta: MetaFunction = () => [
    { title: 'Vendors Current Security Reviews Completed' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, securityReviewId } = params;

        if (
            !vendorId ||
            isNaN(Number(vendorId)) ||
            !securityReviewId ||
            isNaN(Number(securityReviewId))
        ) {
            throw new Error('vendorId or securityReviewId are invalid');
        }

        sharedVendorsDetailsController.loadVendorDetails(Number(vendorId));
        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(securityReviewId) },
        });
        sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewDocuments(
            {
                path: { id: Number(securityReviewId) },
            },
        );
        sharedVendorsSecurityReviewObservationsController.loadSecurityReviewObservations(
            {
                path: { id: Number(securityReviewId) },
                query: {
                    sort: 'CREATED_AT',
                },
            },
        );

        const pageHeaderModel =
            new VendorsSecurityReviewCompletedPageHeaderModel();

        pageHeaderModel.setVendorType('current');

        return {
            pageHeader: pageHeaderModel,
        };
    },
);

const VendorsCurrentSecurityReviewCompleted = (): React.JSX.Element => {
    return (
        <VendorsSecurityReviewCompletedView
            data-testid="VendorsCurrentSecurityReviewCompleted"
            data-id="oHFCbpe5"
        />
    );
};

export default VendorsCurrentSecurityReviewCompleted;
