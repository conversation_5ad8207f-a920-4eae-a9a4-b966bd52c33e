import type { ClientLoader } from '@app/types';
import { sharedVendorsRisksController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { VendorsRisksView } from '@views/vendors-risks';

export const meta: MetaFunction = () => [{ title: 'Vendors Risks' }];

export const clientLoader = action((): ClientLoader => {
    sharedVendorsRisksController.loadVendorRisks();
    sharedVendorsRisksController.loadSettings();

    return {
        pageHeader: {
            title: t`Risks`,
            pageId: 'vendors-risks-page',
        },
    };
});

const VendorsRisks = (): React.JSX.Element => {
    return <VendorsRisksView data-testid="VendorsRisks" data-id="GAB-BcNd" />;
};

export default VendorsRisks;
