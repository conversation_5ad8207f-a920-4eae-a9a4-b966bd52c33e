import { VendorsQuestionnaireLandingModal } from '@components/vendor-hub-questionnaire';
import type { MetaFunction } from '@remix-run/node';

export const meta: MetaFunction = () => [{ title: 'Vendor Questionnaire' }];

const TempVendorHubQuestionnaire = (): React.JSX.Element => {
    return (
        <VendorsQuestionnaireLandingModal
            logoUrl="https://drata.com/images/favicon-32x32.png"
            data-testid="TempVendorHubQuestionnaire"
            data-id="DGrDJsGT"
        />
    );
};

export default TempVendorHubQuestionnaire;
