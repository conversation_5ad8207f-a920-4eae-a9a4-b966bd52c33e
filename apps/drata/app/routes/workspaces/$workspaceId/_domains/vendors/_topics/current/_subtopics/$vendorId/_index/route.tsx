import { type ClientLoaderFunction, redirect } from '@remix-run/react';

export const clientLoader: ClientLoaderFunction = ({ request }) => {
    const url = new URL(request.url);
    const redirectUrl = `${url.pathname}/overview${url.search}${url.hash}`;

    return redirect(redirectUrl, 302);
};

const VendorsCurrentIndex = (): React.JSX.Element => {
    return <div data-testid="VendorsCurrentIndex" data-id="4AUJDYud"></div>;
};

export default VendorsCurrentIndex;
