import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Reports and documents' },
];

const VendorsCurrentReportsAndDocuments = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrentReportsAndDocuments"
            data-id="Bwg9WWyA"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrentReportsAndDocuments;
