import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import type { MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import {
    VENDORS_PROSPECTIVE_SECURITY_REVIEW_FILE_ACTIONS,
    VENDORS_PROSPECTIVE_SECURITY_REVIEW_FILE_PAGE_HEADER_KEY,
    VendorsProspectiveSecurityReviewFilesView,
} from '@views/vendors-prospective-security-review-files';

export const meta: MetaFunction = () => [
    { title: 'Vendors Prospective Security Review File' },
];

export const handle = {
    overrides: {
        pageHeader: {
            title: '<File name>',
            pageId: 'vendors-prospective-temp-security-review-file-page',
            backLink: (
                <AppLink
                    data-id="vendors-prospective-security-review-file-page-back-link"
                    href="/vendors/prospective/1/security-reviews/1"
                    label="Back to Nov 24 security review"
                />
            ),
            actionStack: (
                <ActionStack
                    data-id="vendors-prospective-security-review-file-page-action-stack"
                    gap={dimension3x}
                    stacks={[
                        {
                            actions:
                                VENDORS_PROSPECTIVE_SECURITY_REVIEW_FILE_ACTIONS,
                            id: `${VENDORS_PROSPECTIVE_SECURITY_REVIEW_FILE_PAGE_HEADER_KEY}-actions-stack`,
                        },
                    ]}
                />
            ),
        },
    },
};

const VendorsProspectiveSecurityReviewFile = (): React.JSX.Element => {
    return (
        <VendorsProspectiveSecurityReviewFilesView
            data-testid="VendorsProspectiveSecurityReviewFile"
            data-id="n0HQ_MCO"
        />
    );
};

export default VendorsProspectiveSecurityReviewFile;
