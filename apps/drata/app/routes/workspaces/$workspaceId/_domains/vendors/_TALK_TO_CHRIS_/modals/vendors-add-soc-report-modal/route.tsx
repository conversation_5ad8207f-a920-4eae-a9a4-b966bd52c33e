import type { MetaFunction } from '@remix-run/node';
import { VendorsAddSocReportModalView } from '@views/vendors-add-soc-report-modal';

export const meta: MetaFunction = () => [
    { title: 'Vendors Modals Add Soc Report' },
];

export const handle = {
    overrides: {
        pageHeader: {
            title: 'TEMP Vendor Modals Add Soc Report',
            pageId: 'vendors-temp-modals-add-soc-report-page',
        },
    },
};

const TempVendorsModalsAddSocReportModal = (): React.JSX.Element => {
    return (
        <VendorsAddSocReportModalView
            data-testid="TempVendorsModalsAddSocReportModal"
            data-id="HiTcROGe"
        />
    );
};

export default TempVendorsModalsAddSocReportModal;
