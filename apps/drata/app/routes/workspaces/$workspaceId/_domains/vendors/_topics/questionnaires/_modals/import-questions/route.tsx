import type { MetaFunction } from '@remix-run/node';
import { VendorsQuestionnairesImportQuestionsModalView } from '@views/vendors-questionnaires-import-questions-modal';

export const meta: MetaFunction = () => [
    { title: 'Vendors Questionnaire Import Questions Modal' },
];

export const handle = {
    overrides: {
        pageHeader: {
            title: 'TEMP Vendor Modals Questionnaire Import Questions',
            pageId: 'vendors-temp-modals-import-questions-page',
        },
    },
};

const TempVendorsQuestionnairesImportQuestionsModal = (): React.JSX.Element => {
    return (
        <VendorsQuestionnairesImportQuestionsModalView
            data-testid="TempVendorsQuestionnairesImportQuestionsModal"
            data-id="kdPI2HSv"
        />
    );
};

export default TempVendorsQuestionnairesImportQuestionsModal;
