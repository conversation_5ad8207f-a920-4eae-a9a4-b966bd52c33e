import { isString } from 'lodash-es';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import type { ClientLoader, Topic } from '../../../../../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Vendors' }];
};

export class VendorsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return t`Vendors`;
    }
}

class VendorsPageTopicsNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get topicsNav() {
        const topicsOrder = [
            sharedFeatureAccessModel.isVendorInsightsReadEnabled
                ? 'vendors.insights'
                : null,
            'vendors.current',
            'vendors.prospective',
            sharedFeatureAccessModel.isVendorRiskManagementProEnabled
                ? 'vendors.risks'
                : null,
            'vendors.questionnaires',
            'vendors.settings',
        ].filter(isString);

        const topics: Record<string, Topic> = {
            'vendors.insights': {
                id: 'vendors.insights',
                topicPath: 'vendors/insights',
                label: t`Insights`,
            },
            'vendors.current': {
                id: 'vendors.current',
                topicPath: 'vendors/current',
                label: t`Current vendors`,
            },
            'vendors.prospective': {
                id: 'vendors.prospective',
                topicPath: 'vendors/prospective',
                label: t`Prospective vendors`,
            },
            'vendors.risks': {
                id: 'vendors.risks',
                topicPath: 'vendors/risks',
                label: t`Vendor Risks`,
            },
            'vendors.questionnaires': {
                id: 'vendors.questionnaires',
                topicPath: 'vendors/questionnaires',
                label: t`Questionnaires`,
            },
            'vendors.settings': {
                id: 'vendors.settings',
                topicPath: 'vendors/settings',
                label: t`Settings`,
            },
        };

        return {
            id: 'nav.vendors',
            title: t`Vendors`,
            domainsOrder: ['vendors'],
            domains: {
                vendors: {
                    label: t`Vendors`,
                    hideLabel: true,
                    topicsOrder,
                    topics,
                },
            },
        };
    }
}

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: new VendorsPageHeaderModel(),
        topicsNav: new VendorsPageTopicsNavModel(),
    };
});

const Vendors = (): React.JSX.Element => {
    return <Outlet data-testid="Vendors" data-id="j-ZBNRaq" />;
};

export default Vendors;
