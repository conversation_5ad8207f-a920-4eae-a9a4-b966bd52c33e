import {
    sharedVendorsCurrentController,
    sharedVendorsInsightsController,
} from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { VendorsInsightsView } from '@views/vendors-insights';

export const meta: MetaFunction = () => [{ title: 'Vendors Insights' }];

export const clientLoader: ClientLoaderFunction = action(() => {
    sharedVendorsInsightsController.loadVendorStats();
    sharedVendorsCurrentController.loadVendors({ query: { isArchived: true } });

    return {
        pageHeader: {
            title: t`Vendor Insights`,
            pageId: 'vendors-insights-page',
        },
    };
});

const VendorsInsights = observer((): React.JSX.Element => {
    return (
        <VendorsInsightsView data-testid="VendorsInsights" data-id="ONgt7eE-" />
    );
});

export default VendorsInsights;
