import type { ClientLoader } from '@app/types';
import {
    sharedVendorsDetailsController,
    sharedVendorsFeatureDismissalController,
    sharedVendorsSchedulesQuestionnairesController,
    sharedVendorsSettingsController,
} from '@controllers/vendors';
import { action, when } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import {
    VendorsProfilePageHeaderModel,
    VendorsProfileTabsModel,
} from '@models/vendors-profile';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const clientLoader = action(
    ({ params, request }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId } = params;

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsDetailsController.loadVendorDetails(parseInt(vendorId));
        sharedVendorsSettingsController.loadVendorsSettings();

        // Reactively load additional data when vendor details become available
        when(
            () => Boolean(sharedVendorsDetailsController.vendorDetails?.id),
            () => {
                const vendorDetailsId =
                    sharedVendorsDetailsController.vendorDetails?.id;

                if (!vendorDetailsId) {
                    return;
                }

                // Load feature dismissal state for the vendor
                sharedVendorsFeatureDismissalController.loadFeatureDismissals(
                    vendorDetailsId,
                    'SCHEDULE_QUESTIONNAIRE',
                );

                // Load schedule questionnaires to check if vendor has existing configurations
                sharedVendorsSchedulesQuestionnairesController.loadSchedulesVendorQuestionnaires(
                    {
                        vendorIds: [vendorDetailsId],
                    },
                );
            },
        );

        const parentHref = new URL(getParentRoute(request.url, 2)).pathname;

        const pageHeaderModel = new VendorsProfilePageHeaderModel();
        const tabsModel = new VendorsProfileTabsModel();

        pageHeaderModel.setParentUrl(parentHref);
        tabsModel.setVendorId(Number(vendorId));
        tabsModel.setVendorType('prospective');

        return {
            pageHeader: pageHeaderModel,
            contentNav: tabsModel,
        };
    },
);

const VendorsProspective = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsProspective"
            data-id="J61qBq_o"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsProspective;
