import type { <PERSON>lient<PERSON>oader } from '@app/types';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import {
    VENDORS_QUESTIONNAIRES_PAGE_HEADER_ACTIONS,
    VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY,
} from '@views/vendors-questionnaires';

export const meta: MetaFunction = () => [{ title: 'Vendors Questionnaires' }];

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Questionnaires',
            pageId: 'vendors-questionnaires-page',
            actionStack: (
                <ActionStack
                    data-id="vendors-questionnaires-page-action-stack"
                    gap={dimension3x}
                    stacks={[
                        {
                            actions: VENDORS_QUESTIONNAIRES_PAGE_HEADER_ACTIONS,
                            id: `${VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY}-actions-stack`,
                        },
                    ]}
                />
            ),
        },
    };
});

const VendorsQuestionnaires = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsQuestionnaires"
            data-id="YKRutia8"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsQuestionnaires;
