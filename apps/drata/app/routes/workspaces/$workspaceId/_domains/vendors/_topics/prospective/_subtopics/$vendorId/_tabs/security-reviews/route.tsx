import { parseInt } from 'lodash-es';
import type { ClientLoader } from '@app/types';
import {
    sharedVendorsDetailsController,
    sharedVendorsProfileSecurityReviewsController,
    sharedVendorsSettingsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [
    { title: 'Vendors Prospective Security Reviews' },
];

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId } = params;

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsProfileSecurityReviewsController.setVendorId(
            parseInt(vendorId),
        );

        sharedVendorsDetailsController.loadVendorDetails(parseInt(vendorId));
        sharedVendorsSettingsController.loadVendorsSettings();

        return null;
    },
);

const VendorsProspectiveSecurityReviews = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsProspectiveSecurityReviews"
            data-id="t3GI6QJE"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsProspectiveSecurityReviews;
