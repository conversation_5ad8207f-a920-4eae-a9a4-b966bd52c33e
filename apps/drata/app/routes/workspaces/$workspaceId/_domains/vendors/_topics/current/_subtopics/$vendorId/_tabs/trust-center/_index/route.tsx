import type { ClientLoader } from '@app/types';
import { sharedFeatureAnnouncementDismissalsController } from '@controllers/feature-announcement-dismissals';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileTrustCenterView } from '@views/vendors-profile-trust-center';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Trust Center' },
];

export const clientLoader = action((): ClientLoader => {
    sharedFeatureAnnouncementDismissalsController.loadFeatureDismissal({
        query: { type: 'VENDORS_INTRO_TRUST_CENTER_DOCUMENTS' },
    });

    return null;
});

const VendorsCurrentTrustCenter = (): React.JSX.Element => {
    return (
        <VendorsProfileTrustCenterView
            data-testid="VendorsCurrentTrustCenter"
            data-id="ic65FpqZ"
        />
    );
};

export default VendorsCurrentTrustCenter;
