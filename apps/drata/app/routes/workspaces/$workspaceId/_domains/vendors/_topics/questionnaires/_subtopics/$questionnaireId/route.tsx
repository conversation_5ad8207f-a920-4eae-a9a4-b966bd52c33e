import type { <PERSON><PERSON><PERSON>oader } from '@app/types';
import { sharedVendorsTypeformQuestionnaireController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { action, makeAutoObservable, observer } from '@globals/mobx';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { ErrorBoundaryComponent } from '@ui/error-boundary';
import {
    QUESTIONNAIRES_ADD_HEADER_ACTIONS,
    QUESTIONNAIRES_ADD_HEADER_PAGE_ID,
    VendorsQuestionnairesAddView,
} from '@views/vendors-questionnaires-add';

export const meta: MetaFunction = () => [{ title: 'Vendors Questionnaires' }];

export class VendorsQuestionnairesEditPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = QUESTIONNAIRES_ADD_HEADER_PAGE_ID;

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="vendors-questionnaires-add-page-action-stack"
                gap={dimension3x}
                stacks={[
                    {
                        actions: QUESTIONNAIRES_ADD_HEADER_ACTIONS,
                        id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-actions-stack-0`,
                    },
                ]}
            />
        );
    }

    get title(): string {
        return (
            sharedVendorsTypeformQuestionnaireController.title ||
            'Untitled questionnaire'
        );
    }
}

export const ErrorBoundary = (): React.JSX.Element => {
    return (
        <ErrorBoundaryComponent
            data-testid="ErrorBoundary"
            data-id="PJCtli7m"
            backLink={{
                href: '/vendors/questionnaires',
                label: 'Back to Questionnaires',
            }}
        />
    );
};
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { questionnaireId } = params;

        if (!questionnaireId) {
            throw new Error('Questionnaire ID is required');
        }

        sharedVendorsTypeformQuestionnaireController.loadVendorsQuestionnaire(
            Number(questionnaireId),
        );

        return {
            pageHeader: new VendorsQuestionnairesEditPageHeaderModel(),
        };
    },
);

const VendorsQuestionnairesEdit = observer((): React.JSX.Element => {
    return (
        <VendorsQuestionnairesAddView
            data-testid="VendorsQuestionnairesEdit"
            data-id="hBxXw1W2"
        />
    );
});

export default VendorsQuestionnairesEdit;
