import type { ClientLoader } from '@app/types';
import { sharedUtilitiesObservationsController } from '@controllers/utilities';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
    sharedVendorsSecurityReviewObservationsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { VendorsSecurityReviewPageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [
    { title: 'Vendors Prospective Security Reviews' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { securityReviewId, vendorId } = params;

        if (!securityReviewId || isNaN(Number(securityReviewId))) {
            throw new Error('Invalid securityReviewId');
        }

        sharedVendorsDetailsController.loadVendorDetails(Number(vendorId));

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(securityReviewId) },
        });
        sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewDocuments(
            {
                path: { id: Number(securityReviewId) },
            },
        );
        sharedVendorsSecurityReviewObservationsController.loadSecurityReviewObservations(
            {
                path: { id: Number(securityReviewId) },
            },
        );

        const pageHeaderModel = new VendorsSecurityReviewPageHeaderModel();

        pageHeaderModel.setVendorType('prospective');

        sharedUtilitiesObservationsController.openUtility();

        return {
            pageHeader: pageHeaderModel,
            utilities: {
                utilitiesList: ['observations'],
            },
        };
    },
);

const VendorsProspectiveSecurityReview = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsProspectiveSecurityReview"
            data-id="e6paXWsn"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsProspectiveSecurityReview;
