import { VENDOR_CONTACT_ROLES } from '@components/vendors-current-add-vendor';
import {
    sharedUsersInfiniteController,
    sharedVendorContactsInfiniteController,
} from '@controllers/users';
import { sharedVendorsIntegrationsInfiniteController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import type {
    ClientLoaderFunction,
    ClientLoaderFunctionArgs,
    MetaFunction,
} from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { VendorsCurrentAddVendorView } from '@views/vendors-current-add-vendor';

export const meta: MetaFunction = () => {
    return [{ title: t`Add Vendor` }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ request }: ClientLoaderFunctionArgs) => {
        const parentHref = getParentRoute(request.url);

        sharedUsersInfiniteController.loadUsers({
            roles: [
                'ADMIN',
                'TECHGOV',
                'WORKSPACE_ADMINISTRATOR',
                'POLICY_MANAGER',
            ],
        });

        sharedVendorContactsInfiniteController.loadUsers({
            roles: VENDOR_CONTACT_ROLES,
        });

        sharedVendorsIntegrationsInfiniteController.load();

        return {
            pageHeader: {
                title: t`Add Vendor`,
                pageId: 'vendors-current-add-vendor-page',
                backLink: (
                    <AppLink
                        data-id="vendors-current-add-vendor-page-back-link"
                        href={parentHref}
                        label={t`Back to Current vendors`}
                    />
                ),
            },
        };
    },
);

const VendorsCurrentAddVendor = (): React.JSX.Element => {
    return (
        <VendorsCurrentAddVendorView
            data-testid="VendorsCurrentAddVendor"
            data-id="Qs1ZCd4x"
        />
    );
};

export default VendorsCurrentAddVendor;
