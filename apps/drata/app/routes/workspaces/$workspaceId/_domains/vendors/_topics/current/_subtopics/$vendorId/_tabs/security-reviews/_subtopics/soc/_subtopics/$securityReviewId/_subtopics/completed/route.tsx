import type { ClientLoader } from '@app/types';
import {
    sharedVendorsDetailsController,
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { VendorsSecurityReviewSOCCompletedPageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsSecurityReviewSOCCompletedView } from '@views/vendors-security-review-completed';

export const meta: MetaFunction = () => [
    { title: 'Vendors Current Security Reviews SOC Completed' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, securityReviewId } = params;

        if (
            !vendorId ||
            isNaN(Number(vendorId)) ||
            !securityReviewId ||
            isNaN(Number(securityReviewId))
        ) {
            throw new Error('vendorId or securityReviewId are invalid');
        }

        sharedVendorsDetailsController.loadVendorDetails(Number(vendorId));

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(securityReviewId) },
        });

        sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewSOCDocument(
            {
                path: { id: Number(securityReviewId) },
            },
        );

        sharedVendorsSecurityReviewDocumentsController.setVendorId(
            Number(vendorId),
        );

        sharedVendorsProfileQuestionnaireAISummaryController.saveSocSummary();

        return {
            pageHeader: new VendorsSecurityReviewSOCCompletedPageHeaderModel(),
        };
    },
);

const VendorsCurrentSecurityReviewCompleted = (): React.JSX.Element => {
    return (
        <VendorsSecurityReviewSOCCompletedView
            data-testid="VendorsCurrentSecurityReviewCompleted"
            data-id="705oOi_K"
        />
    );
};

export default VendorsCurrentSecurityReviewCompleted;
