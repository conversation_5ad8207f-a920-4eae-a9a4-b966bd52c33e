import { type ClientLoaderFunction, redirect } from '@remix-run/react';

/**
 * TODO: Add permission check for vendor insights access
 * When the redirect logic is ready, users without sharedFeatureAccessModel.isVendorInsightsReadEnabled
 * permissions should be redirected to /current instead of /insights.
 */
export const clientLoader: ClientLoaderFunction = ({ request }) => {
    const url = new URL(request.url);
    const redirectUrl = `${url.pathname}/insights${url.search}${url.hash}`;

    return redirect(redirectUrl, 302);
};

const VendorsIndex = (): React.JSX.Element => {
    return <div data-testid="VendorsIndex" data-id="4AUJDYud"></div>;
};

export default VendorsIndex;
