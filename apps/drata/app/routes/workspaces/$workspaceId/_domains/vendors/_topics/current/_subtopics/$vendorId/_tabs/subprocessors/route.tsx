import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileSubprocessorsView } from '@views/vendors-profile-subprocessors';

export const meta: MetaFunction = () => [
    { title: 'Vendors Current Subprocesors' },
];

const VendorsCurrentSubprocessors = (): React.JSX.Element => {
    return (
        <VendorsProfileSubprocessorsView
            data-testid="VendorsCurrentSubprocessors"
            data-id="GMNtc1WB"
        />
    );
};

export default VendorsCurrentSubprocessors;
