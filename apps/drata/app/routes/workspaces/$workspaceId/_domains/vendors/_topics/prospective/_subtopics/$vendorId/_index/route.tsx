import { type ClientLoaderFunction, redirect } from '@remix-run/react';

export const clientLoader: ClientLoaderFunction = ({ request }) => {
    const url = new URL(request.url);
    const redirectUrl = `${url.pathname}/overview${url.search}${url.hash}`;

    return redirect(redirectUrl, 302);
};

const VendorsProspectiveIndex = (): React.JSX.Element => {
    return <div data-testid="VendorsProspectiveIndex" data-id="OeSLRSst"></div>;
};

export default VendorsProspectiveIndex;
