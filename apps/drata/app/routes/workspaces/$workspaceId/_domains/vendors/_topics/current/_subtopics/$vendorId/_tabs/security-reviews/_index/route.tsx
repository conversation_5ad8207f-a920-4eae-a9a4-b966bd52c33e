import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewsView } from '@views/vendors-profile-security-reviews';

export const meta: MetaFunction = () => [
    { title: 'Vendors Current Security Reviews' },
];

const VendorsCurrentSecurityReviews = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewsView
            data-testid="VendorsCurrentSecurityReviews"
            data-id="_9vD1ELA"
        />
    );
};

export default VendorsCurrentSecurityReviews;
