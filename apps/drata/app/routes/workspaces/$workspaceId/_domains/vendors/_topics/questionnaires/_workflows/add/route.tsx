import type { ClientLoader } from '@app/types';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import {
    QUESTIONNAIRES_ADD_HEADER_ACTIONS,
    QUESTIONNAIRES_ADD_HEADER_PAGE_ID,
    VendorsQuestionnairesAddView,
} from '@views/vendors-questionnaires-add';

export const meta: MetaFunction = () => [{ title: 'Vendors Questionnaires' }];

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: '<Questionnaire name>',
            pageId: QUESTIONNAIRES_ADD_HEADER_PAGE_ID,
            actionStack: (
                <ActionStack
                    data-id="vendors-questionnaires-add-page-action-stack"
                    gap={dimension3x}
                    stacks={[
                        {
                            actions: QUESTIONNAIRES_ADD_HEADER_ACTIONS,
                            id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-actions-stack-0`,
                        },
                    ]}
                />
            ),
        },
    };
});

const VendorsQuestionnairesAdd = (): React.JSX.Element => {
    return (
        <VendorsQuestionnairesAddView
            data-testid="VendorsQuestionnairesAdd"
            data-id="hBxXw1W2"
        />
    );
};

export default VendorsQuestionnairesAdd;
