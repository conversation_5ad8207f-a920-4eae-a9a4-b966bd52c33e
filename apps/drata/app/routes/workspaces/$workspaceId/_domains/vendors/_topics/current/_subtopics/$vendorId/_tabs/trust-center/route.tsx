import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Trust Center' },
];

const VendorsCurrentTrustCenter = (): React.JSX.Element => {
    return (
        <section data-testid="VendorsCurrentTrustCenter" data-id="Bwg9WWyA">
            <Outlet />
        </section>
    );
};

export default VendorsCurrentTrustCenter;
