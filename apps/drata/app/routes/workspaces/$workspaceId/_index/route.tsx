import type { MetaFunction } from '@remix-run/node';
import { useParams } from '@remix-run/react';

export const meta: MetaFunction = () => [{ title: 'WorkspaceIdIndex' }];

const WorkspaceIdIndex = (): React.JSX.Element => {
    const { workspaceId } = useParams();

    return (
        <div
            data-testid="WorkspaceIdIndex"
            data-id="jPwZmxJb"
            style={{
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',

                    flexGrow: 2,
                }}
            >
                <h1>
                    Select a <i>domain</i>
                </h1>
                <h2>
                    <i>workspace</i>: {workspaceId}
                </h2>

                <p>
                    <strong>This will have an auto-redirect eventually</strong>
                </p>
                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    This temporary experience is to help everyone internally
                    understand the loading strategy of the app.
                </p>
                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    At this point we are aware of which <i>user</i> and{' '}
                    <i>workspace</i> has been selected. This tells us which{' '}
                    <i>domains</i> this <i>user</i>/<i>workspace</i> combo has
                    access to. However we do not know which <i>domain</i> they
                    want to view.
                </p>

                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    This file will be used to control <strong>how</strong> the
                    user is redirected:
                </p>

                <ul
                    style={{
                        width: '30%',
                        margin: '0 auto',
                        textAlign: 'left',
                    }}
                >
                    <li>
                        To the first <i>domain</i> returned by the selected{' '}
                        <i>workspace</i>
                    </li>

                    <li>
                        To the <i>domain</i> they were viewing the last time
                        they visited this <i>workspace</i>
                    </li>

                    <li>
                        Or even some other business logic we have not thought up
                        yet!
                    </li>
                </ul>
            </div>
        </div>
    );
};

export default WorkspaceIdIndex;
