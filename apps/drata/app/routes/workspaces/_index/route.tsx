import type { MetaFunction } from '@remix-run/node';

export const meta: MetaFunction = () => [{ title: 'WorkspacesIndex' }];

const WorkspacesIndex = (): React.JSX.Element => {
    return (
        <div
            data-testid="WorkspacesIndex"
            data-id="jPwZmxJb"
            style={{
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    flexGrow: 2,
                }}
            >
                <h1>
                    Select a <i>workspace</i> within <i>user</i>
                </h1>
                <p>
                    <strong>This will have an auto-redirect eventually</strong>
                </p>
                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    This temporary experience is to help everyone internally
                    understand the loading strategy of the app.
                </p>
                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    At this point we are aware of which <i>user</i> has been
                    selected. This tells us which <i>workspaces</i> this{' '}
                    <i>user</i> has access to. However we do not know which{' '}
                    <i>workspace</i> they want to view.
                </p>

                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    This file will be used to control <strong>how</strong> the
                    user is redirected:
                </p>

                <ul
                    style={{
                        width: '30%',
                        margin: '0 auto',
                        textAlign: 'left',
                    }}
                >
                    <li>
                        To the first <i>workspace</i> returned by the selected{' '}
                        <i>user</i>
                    </li>

                    <li>
                        To the <i>workspace</i> they were viewing the last time
                        they visited this <i>user</i>
                    </li>

                    <li>
                        Or even some other business logic we have not thought up
                        yet!
                    </li>
                </ul>
            </div>
        </div>
    );
};

export default WorkspacesIndex;
