import { AppDatatable } from '@components/app-datatable';
import { sharedControlPoliciesController } from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { sharedPolicyDetailsController } from '@controllers/policies';
import { action, observer } from '@globals/mobx';
import { PolicyDetailsPanelView } from '@views/policy-details-panel';
import { CONTROLS_POLICIES_COLUMNS } from '../constants/columns.constant';
import { CONTROLS_POLICIES_FILTERS } from '../constants/filters.constant';

const openPolicyDetailsPanel = action((policyId: number): void => {
    sharedPolicyDetailsController.loadPolicyDetails(policyId);
    sharedPolicyDetailsController.loadPolicyControlsAssociated(policyId);
    sharedPolicyDetailsController.loadPolicyFrameworksAssociated(policyId);

    panelController.openPanel({
        id: 'policy-details-panel',
        content: () => <PolicyDetailsPanelView data-id="_33iIY" />,
    });
});

export const ControlsPoliciesView = observer((): React.JSX.Element => {
    const { controlPolicies, isLoading, total, loadControlPoliciesPage } =
        sharedControlPoliciesController;

    return (
        <AppDatatable
            // isRowSelectionEnabled
            isLoading={isLoading}
            tableId="datatable-controls-policies-list"
            total={total}
            data={controlPolicies}
            columns={CONTROLS_POLICIES_COLUMNS}
            filterProps={CONTROLS_POLICIES_FILTERS}
            data-id="Tl2tQxdI"
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        level: 'secondary',
                        label: 'Download policies',
                    },
                },
                {
                    actionType: 'button',
                    id: 'link-policies-button',
                    typeProps: {
                        level: 'secondary',
                        label: 'Link policies',
                    },
                },
            ]}
            onFetchData={loadControlPoliciesPage}
            onRowClick={({ row }) => {
                openPolicyDetailsPanel(row.policy.id);
            }}
        />
    );
});
