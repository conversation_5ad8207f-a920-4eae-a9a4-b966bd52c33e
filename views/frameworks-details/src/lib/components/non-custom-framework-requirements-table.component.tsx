import { useCallback, useMemo } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedRequirementsController } from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useLocation, useNavigate } from '@remix-run/react';
import { getFrameworksDetailsColumns } from '../../constants/columns.constant';
import {
    getRequirementsFirstTimeEmptyStateProps,
    getRequirementsNoResultsEmptyStateProps,
} from '../helpers/requirements-empty-state-helpers';
import { sharedFrameworkDetailsRequirementListModel } from '../models/framework-details-requirement-list-model';
import { sharedNonCustomFrameworkRequirementsBulkActionsModel } from '../models/non-custom-framework-requirements-bulk-actions.model';

export const NonCustomFrameworkRequirementsTable = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const location = useLocation();

        const { requirementListFilters, tableActions } =
            sharedFrameworkDetailsRequirementListModel;

        const {
            loadRequirements,
            requirements,
            requirementsTotal,
            requirementsIsLoading,
            shouldShowFirstTimeEmpty,
        } = sharedRequirementsController;

        const { bulkActions, handleRowSelection } =
            sharedNonCustomFrameworkRequirementsBulkActionsModel;

        const navigateToRequestDetail = useCallback(
            (requestId: number) => {
                navigate(`${location.pathname}/${requestId}`);
            },
            [location.pathname, navigate],
        );

        const columns = useMemo(() => getFrameworksDetailsColumns(), []);

        const emptyState = shouldShowFirstTimeEmpty
            ? getRequirementsFirstTimeEmptyStateProps()
            : getRequirementsNoResultsEmptyStateProps();

        return (
            <AppDatatable
                // isRowSelectionEnabled
                isFullPageTable
                tableId="requirements-list"
                isLoading={requirementsIsLoading}
                total={requirementsTotal}
                data={requirements}
                columns={columns}
                filterProps={requirementListFilters}
                tableActions={tableActions}
                bulkActionDropdownItems={bulkActions}
                data-id="NonCustomFrameworkRequirementsTable"
                emptyStateProps={emptyState}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onRowSelection={handleRowSelection}
                onFetchData={loadRequirements}
                onRowClick={({ row }) => {
                    navigateToRequestDetail(row.id);
                }}
            />
        );
    },
);
