import { isEmpty } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import {
    activeMonitoringCodeDetailsController,
    sharedFindingsController,
    sharedFindingsFiltersController,
} from '@controllers/monitoring-details';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import type { DatatableProps, FilterProps } from '@cosmos/components/datatable';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import type { FindingItemResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { getDatatableStructure } from './helpers/structure-helpers';

export const MonitoringDetailsFindingsView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const {
        resultStatus,
        monitoringControlInstance,
        isLoading: isLoadingMonitoringControlInstance,
    } = activeMonitoringCodeDetailsController;

    const { findingsList, findingsListTotal, isLoadingFindingsList } =
        sharedFindingsController;

    const { findingsFilters, isLoadingFindingsFilters } =
        sharedFindingsFiltersController;

    if (
        isLoadingFindingsFilters ||
        isLoadingMonitoringControlInstance ||
        isEmpty(findingsFilters)
    ) {
        return <Loader isSpinnerOnly label={'Loading...'} />;
    }

    if (resultStatus === 'ERROR') {
        return (
            <Stack
                data-testid="MonitoringDetailsFindingsViewErrorResultStatus"
                data-id="mEYSmngD"
                height="75%"
                justify="center"
                align="center"
            >
                <Box width={breakpointMd}>
                    <EmptyState
                        title="No findings due to error"
                        description="This test was unable to generate findings due to an error that prevented it from running. Please review the details of the error to resolve it and try again."
                        data-id="mEYSmngD"
                        data-testid="MonitoringDetailsFindingsViewErrorResultStatus"
                        illustrationName="Warning"
                        leftAction={
                            <Button
                                label="View error details"
                                level="secondary"
                                onClick={() => {
                                    navigate(
                                        `/compliance/monitoring/details/${monitoringControlInstance?.id}/overview`,
                                    );
                                }}
                            />
                        }
                    />
                </Box>
            </Stack>
        );
    }

    const {
        category,
        source,
        testId,
        additionalProperties,
        filters: filterValues,
    } = findingsFilters;

    const { columns: structureColumns, filters } = getDatatableStructure({
        category,
        source,
        testId,
        filtersValues: Object.fromEntries(
            Object.entries(filterValues).map(([key, value]) => [
                key,
                Array.isArray(value) ? value : [value],
            ]),
        ),
        additionalProperties: additionalProperties.map((a) => ({
            id: a.id,
        })),
    });

    return (
        <AppDatatable
            // isRowSelectionEnabled
            isLoading={isLoadingFindingsList || isLoadingFindingsFilters}
            tableId="datatable-findings"
            data-id="datatable-findings"
            data={findingsList}
            total={findingsListTotal}
            data-testid="MonitoringDetailsFindingsView"
            columns={
                structureColumns as DatatableProps<FindingItemResponseDto>['columns']
            }
            // Only render filterProps when filters array exists and has items otherwise the prop just can be undefined
            filterProps={
                isEmpty(filters)
                    ? undefined
                    : {
                          filters: filters as FilterProps['filters'],
                          clearAllButtonLabel: 'Reset filters',
                          triggerLabel: 'Filter',
                      }
            }
            emptyStateProps={{
                title: 'No findings for this test',
                description:
                    'Test results you need to address will appear here.',
                illustrationName: 'AddCircle',
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: 'Pin filters to page',
                    toggleUnpinnedLabel: 'Move filters to dropdown',
                },
                viewMode: 'toggleable',
            }}
            bulkActionDropdownItems={[
                {
                    actionType: 'button',
                    id: 'bulk-actions-findings-dropdown',
                    typeProps: {
                        label: 'Exclude findings',
                        level: 'tertiary',
                    },
                },
            ]}
            onRowClick={({ row }) => {
                navigate(
                    `/compliance/monitoring/details/${row.testId}/findings/${row.id}`,
                );
            }}
            onFetchData={(params) => {
                sharedFindingsController.load({
                    testId: Number(testId),
                    ...params,
                });
            }}
        />
    );
});
