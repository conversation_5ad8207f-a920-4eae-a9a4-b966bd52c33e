import { AppDatatable } from '@components/app-datatable';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import {
    AUDIT_HUB_AUDITORS_LIST_TABLE_COLUMNS,
    EMPTY_STATE_PROPS,
    FILTER_VIEW_MODE_PROPS,
    TABLE_SEARCH_PROPS,
} from './audit-hub-audits-details-page-constants';

export const AuditHubAuditDetailsRequestTable = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const {
            customerRequests,
            isLoading,
            total,
            getCustomerRequestList,
            filterProps,
        } = sharedCustomerRequestsController;
        const { clientId } = sharedCustomerRequestDetailsController;

        const { auditSummaryByIdData } = sharedAuditorController;

        const navigateToRequestDetail = (requestId: number) => {
            navigate(
                `/audit-hub/clients/${clientId}/audits/${auditSummaryByIdData?.auditorFrameworkId}/evidence-requests/${requestId}/overview`,
            );
        };

        return (
            <AppDatatable
                // isRowSelectionEnabled
                isLoading={isLoading}
                tableId="audit-hub-datatable-audit-details-requests"
                total={total}
                data={customerRequests}
                columns={AUDIT_HUB_AUDITORS_LIST_TABLE_COLUMNS}
                filterProps={filterProps}
                data-testid="AuditHubAuditDetailsRequestTable"
                data-id=""
                tableSearchProps={TABLE_SEARCH_PROPS}
                filterViewModeProps={FILTER_VIEW_MODE_PROPS}
                emptyStateProps={EMPTY_STATE_PROPS}
                tableActions={[
                    {
                        actionType: 'dropdown',
                        id: 'add-request-dropdown',
                        typeProps: {
                            label: 'Add Request',
                            level: 'secondary',
                            endIconName: 'ChevronDown',
                            align: 'end',
                            items: [
                                {
                                    id: 'add-single-request',
                                    label: 'Add single request',
                                    type: 'item',
                                    value: 'ADD_SINGLE',
                                    onClick: () => {
                                        // TODO: Implement add single request https://drata.atlassian.net/browse/ENG-68583
                                    },
                                },
                                {
                                    id: 'add-bulk-requests',
                                    label: 'Add requests in bulk',
                                    type: 'item',
                                    value: 'ADD_BULK',
                                    onClick: () => {
                                        // TODO: Implement add bulk requests https://drata.atlassian.net/browse/ENG-68583
                                    },
                                },
                            ],
                        },
                    },
                ]}
                onFetchData={getCustomerRequestList}
                onRowClick={({ row }) => {
                    navigateToRequestDetail(row.id);
                }}
            />
        );
    },
);
