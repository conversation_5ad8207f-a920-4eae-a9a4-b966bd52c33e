import { useCallback, useMemo } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedComplianceAsCodeRepositoriesListController } from '@controllers/compliance-as-code-list-repositories';
import { panelController } from '@controllers/panel';
import type {
    FilterProps,
    TableAction,
    TableSettingsTriggerProps,
} from '@cosmos/components/datatable';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import { observer } from '@globals/mobx';
import { ComplianceAsCodeRepositoryDetailView } from '@views/compliance-as-code-repository-detail';
import { COLUMNS } from './constants/compliance-as-code-repositories-columns.constants';
import type { ComplianceAsCodeRepositoriesDatatable } from './types/compliance-as-code-repositories-data-table.types';

export const SettingsComplianceAsCodeRepositoriesView = observer(
    (): React.JSX.Element => {
        const { isLoading, repositories, loadRepositories } =
            sharedComplianceAsCodeRepositoriesListController;

        const openPanelHandler = useCallback(
            (repository: { row: ComplianceAsCodeRepositoriesDatatable }) => {
                panelController.openPanel({
                    id: 'compliance-as-code-repository-detail-panel',
                    queryParams: {
                        repositoryId: repository.row.repositoryId,
                    },
                    content: () => (
                        <ComplianceAsCodeRepositoryDetailView data-id="LxHMRyse" />
                    ),
                });
            },
            [],
        );

        const tableActions: TableAction[] = useMemo(
            () => [
                {
                    actionType: 'button',
                    id: 'sync-repository-button',
                    typeProps: {
                        level: 'secondary',
                        label: 'Resync',
                    },
                },
            ],
            [],
        );

        const tableSettingsTriggerProps: TableSettingsTriggerProps = useMemo(
            () => ({
                actionType: 'button',
                id: 'table-settings-trigger',
                typeProps: {
                    isIconOnly: true,
                    startIconName: 'Settings',
                    colorScheme: 'neutral',
                    align: 'start',
                    label: 'Settings',
                    level: 'tertiary',
                },
            }),
            [],
        );

        const emptyStateProps: EmptyStateProps = useMemo(
            () => ({
                title: 'Compliance as code repositories',
                description: 'No repositories found',
                illustrationName: 'Warning',
            }),
            [],
        );

        const filterProps: FilterProps = useMemo(
            () => ({
                clearAllButtonLabel: 'Reset',
                filters: [
                    {
                        filterType: 'radio',
                        id: 'scanEnabled',
                        label: 'Testing',
                        options: [
                            {
                                label: 'Enabled',
                                value: 'true',
                            },
                            {
                                label: 'Disabled',
                                value: 'false',
                            },
                        ],
                    },
                ],
                triggerLabel: 'Filters',
            }),
            [],
        );

        return (
            <AppDatatable
                // isRowSelectionEnabled
                isLoading={isLoading}
                tableId="datatable-settings-compliance-as-code-repositories"
                data-id="datatable-settings-compliance-as-code-repositories"
                columns={COLUMNS}
                total={repositories?.total ?? 0}
                data={repositories?.data ?? []}
                data-testid="SettingsComplianceAsCodeRepositoriesView"
                tableActions={tableActions}
                tableSettingsTriggerProps={tableSettingsTriggerProps}
                emptyStateProps={emptyStateProps}
                filterProps={filterProps}
                onRowClick={openPanelHandler}
                onFetchData={loadRepositories}
            />
        );
    },
);
