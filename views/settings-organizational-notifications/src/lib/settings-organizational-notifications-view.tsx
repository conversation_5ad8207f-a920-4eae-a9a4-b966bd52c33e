import { AppDatatable } from '@components/app-datatable';
import { Box } from '@cosmos/components/box';
import { NOTIFICATION_FREQUENCY_LABELS } from './constants/notification-frequency-labels.constant';
import { NOTIFICATION_PROVIDER_LABELS } from './constants/notification-provider-labels.constant';
import { NOTIFICATION_TYPE_LABELS } from './constants/notification-type-labels.constant';
import { NOTIFICATIONS_TABLE_ACTIONS } from './constants/notifications-table-actions.constant';
import { NOTIFICATIONS_TABLE_BULK_ACTION_OPTIONS } from './constants/notifications-table-bulk-action-options.constant';
import { NOTIFICATIONS_TABLE_COLUMNS } from './constants/notifications-table-columns.constant';
import { NOTIFICATIONS_TABLE_EMPTY_STATE_PROPS } from './constants/notifications-table-empty-state-props.constant';
import { NOTIFICATIONS_TABLE_FILTER_PROPS } from './constants/notifications-table-filter-props.constant';
import { NOTIFICATIONS_TABLE_FILTER_VIEW_MOD_PROPS } from './constants/notifications-table-filter-view-mod-props.constant';
import { NOTIFICATIONS_TABLE_PAGINATION_OPTIONS } from './constants/notifications-table-pagination-options.constant';
import { NOTIFICATIONS_TABLE_SEARCH_PROPS } from './constants/notifications-table-search-props.constant';
import type { NotificationRuleType } from './types/notification-rule';

const MOCK_DATA: NotificationRuleType[] = [
    {
        type: NOTIFICATION_TYPE_LABELS.CONTROL_MONITOR,
        status: 'ENABLED',
        provider: {
            name: NOTIFICATION_PROVIDER_LABELS.SLACK,
            logo: '/some/route',
            account: '[account]',
        },
        destination: '#channel-name, #channel-name',
        frequency: NOTIFICATION_FREQUENCY_LABELS.WEEKLY,
    },
    {
        type: NOTIFICATION_TYPE_LABELS.NON_COMPLIANT_PERSONNEL,
        status: 'DISABLED',
        provider: {
            name: NOTIFICATION_PROVIDER_LABELS.MICROSOFT_TEAMS,
            logo: '/some/route',
            account: '[account]',
        },
        destination: '#channel-name, #channel-name',
        frequency: NOTIFICATION_FREQUENCY_LABELS.DAILY,
    },
    {
        type: NOTIFICATION_TYPE_LABELS.TEST_WITH_ERROR,
        status: 'ENABLED',
        provider: {
            name: NOTIFICATION_PROVIDER_LABELS.EMAIL,
            logo: '/some/route',
        },
        destination: '#channel-name, #channel-name',
        frequency: NOTIFICATION_FREQUENCY_LABELS.NONE,
    },
];

export const SettingsOrganizationalNotificationsView =
    (): React.JSX.Element => {
        return (
            <Box
                p="8x"
                data-testid="SettingsOrganizationalNotificationsView"
                data-id="0BNk6AG1"
            >
                <AppDatatable
                    // isRowSelectionEnabled
                    tableId="dt-faf-fields"
                    data-id="dt-faf-fields"
                    columns={NOTIFICATIONS_TABLE_COLUMNS}
                    data={MOCK_DATA}
                    total={MOCK_DATA.length}
                    isLoading={false}
                    tableActions={NOTIFICATIONS_TABLE_ACTIONS}
                    filterProps={NOTIFICATIONS_TABLE_FILTER_PROPS}
                    tableSearchProps={NOTIFICATIONS_TABLE_SEARCH_PROPS}
                    emptyStateProps={NOTIFICATIONS_TABLE_EMPTY_STATE_PROPS}
                    bulkActionDropdownItems={
                        NOTIFICATIONS_TABLE_BULK_ACTION_OPTIONS
                    }
                    defaultPaginationOptions={
                        NOTIFICATIONS_TABLE_PAGINATION_OPTIONS
                    }
                    filterViewModeProps={
                        NOTIFICATIONS_TABLE_FILTER_VIEW_MOD_PROPS
                    }
                />
            </Box>
        );
    };
