import { AppDatatable } from '@components/app-datatable';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { AUDITORS_LIST_TABLE_COLUMNS } from '../constants/audits-details-page.constants';
import { getFilterProps } from '../helpers/audit-details-filter.helper';

export const AuditDetailsRequestTable = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const {
        customerRequests,
        isLoading,
        total,
        getCustomerRequestList,
        totalUnreadMessages,
    } = sharedCustomerRequestsController;

    const { auditSummaryByIdData } = sharedAuditorController;
    const navigateToRequestDetail = (requestId: number) => {
        navigate(
            `/compliance/audits/${auditSummaryByIdData?.auditorFrameworkId}/request/${requestId}/details`,
        );
    };

    return (
        <AppDatatable
            // isRowSelectionEnabled
            isLoading={isLoading}
            tableId="datatable-audit-details-requests"
            total={total}
            data={customerRequests}
            columns={AUDITORS_LIST_TABLE_COLUMNS}
            filterProps={getFilterProps(totalUnreadMessages)}
            data-testid="AuditDetailsRequestTable"
            data-id="ojxb0h8q"
            tableSettingsTriggerProps={{
                actionType: 'button',
                id: 'table-settings-trigger',
                typeProps: {
                    align: 'start',
                    isIconOnly: true,
                    label: 'Table Settings',
                    level: 'secondary',
                    startIconName: 'Settings',
                },
            }}
            tableSearchProps={{
                hideSearch: false,
                placeholder: 'Search',
                defaultValue: '',
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: 'Pin filters to page',
                    toggleUnpinnedLabel: 'Move filters to dropdown',
                },
                viewMode: 'toggleable',
            }}
            emptyStateProps={{
                title: "We couldn't find any matches",
                description: 'Try expanding your filter or search criteria.',
            }}
            onFetchData={getCustomerRequestList}
            onRowClick={({ row }) => {
                navigateToRequestDetail(row.id);
            }}
        />
    );
});
