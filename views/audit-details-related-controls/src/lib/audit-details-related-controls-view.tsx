import { AppDatatable } from '@components/app-datatable';
import { Grid } from '@cosmos/components/grid';
import { AUDIT_RELATED_CONTROLS_COLUMNS } from '../constants/columns.constant';
import type { AuditDetailsRelatedControlsType } from '../types/audit-details-related-controls.type';

const AUDIT_DETAILS_RELATED_CONTROLS_DATA: AuditDetailsRelatedControlsType[] = [
    {
        code: 'DCF-1333',
        title: 'Background Checks',
    },
    {
        code: 'DCF-992',
        title: 'Contractor Requirements',
    },
    {
        code: 'DCF-123',
        title: 'Code of Conduct',
    },
];

export const AuditDetailsRelatedControlsView = (): React.JSX.Element => {
    return (
        <Grid
            gap="4x"
            data-testid="AuditDetailsRelatedControlsView"
            data-id="Dr_4k4xh"
        >
            <AppDatatable
                // isRowSelectionEnabled
                isLoading={false}
                tableId="audit-related-controls-list"
                total={AUDIT_DETAILS_RELATED_CONTROLS_DATA.length}
                data={AUDIT_DETAILS_RELATED_CONTROLS_DATA}
                columns={AUDIT_RELATED_CONTROLS_COLUMNS}
                bulkActionDropdownItems={[
                    {
                        actionType: 'dropdown',
                        id: 'bulk-actions-dropdown',
                        typeProps: {
                            items: [
                                {
                                    id: 'download',
                                    label: 'Download selected',
                                    type: 'item',
                                    value: 'download',
                                    startIconName: 'Download',
                                },
                            ],
                            label: 'Download',
                            level: 'tertiary',
                        },
                    },
                ]}
            />
        </Grid>
    );
};
