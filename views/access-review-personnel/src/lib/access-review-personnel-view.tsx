import { isEmpty, isNil, isObject } from 'lodash-es';
import { useCallback, useMemo, useState } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedAccessReviewApplicationGroupsController } from '@controllers/access-reviews';
import {
    activeAccessReviewsApplicationsController,
    activeAccessReviewsApplicationsUserController,
} from '@controllers/access-reviews-applications';
import type {
    FetchDataResponseParams,
    GlobalFilterState,
} from '@cosmos/components/datatable';
import type { UserAccessReviewApplicationResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { accessReviewPersonnelAdapter } from './adapters/access-review-personnel.adapter';
import { getColumns } from './constants/applications-personnel-user.constants';
import { createAccessReviewFiltersModel } from './models/access-review-filters.model';

export const AccessReviewPersonnelView = observer((): JSX.Element => {
    const [isSummaryView, setIsSummaryView] = useState(true);
    const navigate = useNavigate();

    // =========================================================================
    // Controller data
    // =========================================================================
    const {
        accessReviewApplicationUsersList,
        accessReviewApplicationUsersTotal,
        isLoading,
        loadAccessReviewApplicationUsers,
        updateFilterValues,
        accessReviewApplicationConnections,
    } = activeAccessReviewsApplicationsUserController;

    const { currentWorkspace, isLoading: isWorkspaceLoading } =
        sharedWorkspacesController;

    const {
        isManuallyAddedApplication,
        isLoading: isLoadingSource,
        accessReviewsApplicationsDetails,
        warnings,
    } = activeAccessReviewsApplicationsController;

    const {
        isLoading: isLoadingGroups,
        hasNextPage,
        accessReviewApplicationGroupsList,
    } = sharedAccessReviewApplicationGroupsController;

    // Create a non-reactive wrapper to avoid MobX tracking issues
    const loadAccessReviewApplicationUsersConnections = useCallback(() => {
        activeAccessReviewsApplicationsUserController.loadAccessReviewApplicationUsersConnections();
    }, []);

    const { clientType, id, name, source } = accessReviewsApplicationsDetails;

    const isLoadingData =
        isLoading || isLoadingSource || isLoadingGroups || isWorkspaceLoading;

    const workspaceId = currentWorkspace?.id ?? 1;

    // Memoize the updateFilterValues callback to prevent model recreation
    const memoizedUpdateFilterValues = useCallback(
        (filters: GlobalFilterState['filters']) => {
            updateFilterValues(filters);
        },
        [updateFilterValues],
    );

    // Memoize the loadApplicationGroups callback to prevent model recreation
    const memoizedLoadApplicationGroups = useCallback(
        (params: { search?: string; applicationId?: number }) => {
            sharedAccessReviewApplicationGroupsController.loadApplicationGroups(
                params,
            );
        },
        [],
    );

    // =========================================================================
    // Filters model
    // =========================================================================
    const filtersModel = useMemo(
        () =>
            createAccessReviewFiltersModel({
                clientType,
                source,
                isManuallyAddedApplication,
                accessReviewApplicationGroupsList,
                loadAccessReviewApplicationGroupsForPersonnel:
                    memoizedLoadApplicationGroups,
                accessReviewApplicationConnections,
                loadAccessReviewApplicationUsersConnections,
                warnings,
                updateFilterValues: memoizedUpdateFilterValues,
                hasMoreGroups: hasNextPage,
                isLoadingGroups,
            }),
        [
            clientType,
            source,
            isManuallyAddedApplication,
            accessReviewApplicationGroupsList,
            accessReviewApplicationConnections,
            loadAccessReviewApplicationUsersConnections,
            warnings,
            memoizedUpdateFilterValues,
            hasNextPage,
            isLoadingGroups,
            memoizedLoadApplicationGroups,
        ],
    );

    const { warningFiltersObject, tableFilters } = filtersModel;

    // Track current filter state for hasActiveFilters calculation
    const [currentFilters, setCurrentFilters] = useState<
        FetchDataResponseParams['globalFilter']['filters'] | null
    >(null);

    // Calculate hasActiveFilters from current filter state
    const hasActiveFilters = useMemo(() => {
        if (!currentFilters) {
            return false;
        }

        return Object.values(currentFilters).some((filter) => {
            const { value } = filter;

            if (Array.isArray(value)) {
                if (isEmpty(value)) {
                    return false;
                }

                return !value.every((item: unknown) => {
                    const itemValue =
                        isObject(item) && 'value' in item
                            ? (item as { value: unknown }).value
                            : item;

                    return (
                        itemValue === 'ALL_CURRENT_PERSONNEL' ||
                        itemValue === 'ALL_PERSONNEL'
                    );
                });
            }

            if (isNil(value) || value === '') {
                return false;
            }

            const valueToCheck =
                isObject(value) && 'value' in value
                    ? (value as { value: unknown }).value
                    : value;
            const stringValue = String(valueToCheck);
            const isDefaultAllValue =
                stringValue === 'ALL_REVIEW_STATUS' ||
                stringValue === 'ALL_PERMISSIONS' ||
                stringValue === 'ALL_CURRENT_PERSONNEL' ||
                stringValue === 'ALL_PERSONNEL' ||
                stringValue === '';

            return !isDefaultAllValue;
        });
    }, [currentFilters]);

    // Handle data fetching with filter cleanup handled by model
    const handleFetchData = useCallback(
        (fetchParams: FetchDataResponseParams | null) => {
            if (!fetchParams) {
                return;
            }

            // Update current filters for hasActiveFilters calculation
            setCurrentFilters(fetchParams.globalFilter.filters);

            // Process and clean filter values - model returns cleaned params
            const cleanedParams = filtersModel.handleFilterChange(fetchParams);

            // Load data with cleaned params
            if (cleanedParams) {
                loadAccessReviewApplicationUsers(cleanedParams);
            }
        },
        [loadAccessReviewApplicationUsers, filtersModel],
    );

    // =========================================================================
    // Event handlers
    // =========================================================================

    /**
     * Handles changing between summary and detailed views.
     */
    const handleSummaryViewChange = useCallback((value: boolean) => {
        setIsSummaryView(value);
    }, []);

    /**
     * Handles row click to navigate to personnel details.
     */
    const handleRowClick = useCallback(
        ({ row }: { row: UserAccessReviewApplicationResponseDto }) => {
            navigate(
                `/workspaces/${workspaceId}/governance/access-review/applications/${id}/personnel/${row.clientType}/user/${row.id}`,
            );
        },
        [navigate, workspaceId, id],
    );

    // =========================================================================
    // Memoized values
    // =========================================================================
    const filteredColumns = useMemo(
        () =>
            getColumns().filter((column) =>
                column.showIf({
                    isSummaryView,
                    isManuallyAddedApplication,
                    missingMFA: warningFiltersObject.warningFilters.missingMFA,
                }),
            ),
        [
            isSummaryView,
            isManuallyAddedApplication,
            warningFiltersObject.warningFilters.missingMFA,
        ],
    );

    // Use the adapter to transform data for the view
    const {
        tableData,
        totalCount,
        tableSettingsTriggerProps,
        tableSearchProps,
        emptyStateProps,
        filterViewModeProps,
    } = useMemo(
        () =>
            accessReviewPersonnelAdapter({
                accessReviewApplicationUsersList,
                accessReviewApplicationUsersTotal,
                isManuallyAddedApplication,
                name,
                isSummaryView,
                handleSummaryViewChange,
                hasActiveFilters,
            }),
        [
            accessReviewApplicationUsersList,
            accessReviewApplicationUsersTotal,
            isManuallyAddedApplication,
            name,
            isSummaryView,
            handleSummaryViewChange,
            hasActiveFilters,
        ],
    );

    return (
        <AppDatatable
            isFullPageTable
            key={`${clientType}-${source}-${isManuallyAddedApplication}`}
            isLoading={isLoadingData}
            tableId="personnel-datatable"
            data-testid="PersonnelTable"
            data-id="personnel-datatable"
            total={totalCount}
            columns={filteredColumns}
            filterProps={tableFilters}
            emptyStateProps={emptyStateProps}
            tableSearchProps={tableSearchProps}
            filterViewModeProps={filterViewModeProps}
            tableSettingsTriggerProps={tableSettingsTriggerProps}
            data={tableData}
            onFetchData={handleFetchData}
            onRowClick={({ row }) => {
                handleRowClick({
                    row,
                });
            }}
        />
    );
});
