import { AppDatatable } from '@components/app-datatable';
import { openLinkControlsModalWithWorkspace } from '@components/evidence-library';
import {
    sharedAuditHubController,
    sharedAuditHubControlsController,
} from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { observer } from '@globals/mobx';
import { EVIDENCE_REQUEST_DETAILS_CONTROLS_COLUMNS } from './constants/evidence-controls-columns';
import { openControlDetailsPanel } from './helpers/open-control-details-panel.helper';

export const EvidenceRequestDetailsControlsView = observer((): JSX.Element => {
    const {
        isEvidencePackageDownloading,
        downloadSelectedControls,
        downloadAllControls,
        handleRowSelection,
        updateRequestControls,
    } = sharedCustomerRequestDetailsController;

    const {
        auditCustomerRequestControls,
        auditCustomerRequestControlsIsLoading,
        auditCustomerRequestControlsTotal,
        loadControlsPage,
    } = sharedAuditHubControlsController;

    const { auditByIdData } = sharedAuditHubController;
    const workspaceId = auditByIdData?.framework.productId;

    return (
        <AppDatatable
            // isRowSelectionEnabled
            hidePagination
            isLoading={auditCustomerRequestControlsIsLoading}
            tableId="datatable-audit-hub-evidence-request-details-controls"
            data={auditCustomerRequestControls}
            total={auditCustomerRequestControlsTotal}
            data-testid="EvidenceRequestDetailsControlsView"
            data-id="U9j_XYCl"
            columns={EVIDENCE_REQUEST_DETAILS_CONTROLS_COLUMNS}
            defaultColumnOptions={{
                minSize: 5,
            }}
            defaultPaginationOptions={{
                pageSizeOptions: [5, 10, 20, 50],
                pageSize: 10,
                pageIndex: 0,
            }}
            bulkActionDropdownItems={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        label: 'Download Selection',
                        level: 'tertiary',
                        onClick: downloadSelectedControls,
                    },
                },
            ]}
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        isIconOnly: false,
                        label: 'Download all',
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        isLoading: isEvidencePackageDownloading,
                        a11yLoadingLabel: 'Downloading evidence package',
                        onClick: downloadAllControls,
                    },
                },
                {
                    actionType: 'button',
                    id: 'map-controls-button',
                    typeProps: {
                        label: 'Map controls',
                        level: 'secondary',
                        onClick: () => {
                            // Get current control IDs to exclude from the modal
                            const currentControlIds =
                                auditCustomerRequestControls
                                    .map((control) => control.id)
                                    .filter((id) => id !== undefined);

                            openLinkControlsModalWithWorkspace({
                                objectType: 'risk',
                                onConfirm: (selectedControls) => {
                                    const controlIds = selectedControls.map(
                                        (item) => item.controlData.id,
                                    );

                                    updateRequestControls(controlIds);
                                },
                                excludeControlIds: currentControlIds,
                                workspaceId,
                            });
                        },
                    },
                },
            ]}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: 'Controls',
                description: 'No controls were found',
            }}
            onFetchData={loadControlsPage}
            onRowSelection={handleRowSelection}
            onRowClick={({ row }) => {
                openControlDetailsPanel(row);
            }}
        />
    );
});
