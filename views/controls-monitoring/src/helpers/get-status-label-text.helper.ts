import type { MonitorTrackStatus } from '@controllers/monitoring-details';
import { t } from '@globals/i18n/macro';

export function getStatusLabelText(
    status: MonitorTrackStatus | undefined,
): string {
    switch (status) {
        case 'PASSED': {
            return t`Days passed`;
        }
        case 'FAILED': {
            return t`Days failed`;
        }
        case 'ERROR': {
            return t`Days erroring`;
        }
        case 'READY':
        case 'PREAUDIT': {
            return t`No test results`;
        }
        default: {
            return t`No test results`;
        }
    }
}
