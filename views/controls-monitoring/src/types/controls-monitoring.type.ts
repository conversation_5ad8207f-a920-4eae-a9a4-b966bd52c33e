import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';

export interface ControlMonitoringType {
    name: string;
    checkResultStatus: MonitorV2ControlTestInstanceOverviewResponseDto['checkResultStatus'];
    checkTypes: MonitorV2ControlTestInstanceOverviewResponseDto['checkTypes'];
    findings: string;
    availableConnections: MonitorV2ControlTestInstanceOverviewResponseDto['availableConnections'];
    draft: boolean;
    testId: number;
}

export interface Control {
    id: number;
    code: string;
    isReady: boolean;
    controlNumber: number;
}
