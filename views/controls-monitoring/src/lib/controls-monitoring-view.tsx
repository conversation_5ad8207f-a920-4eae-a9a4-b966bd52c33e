import { AppDatatable } from '@components/app-datatable';
import {
    activeTicketsMetadataController,
    activeTrackCardController,
    sharedMonitorFindingsController,
    sharedMonitoringDetailsControlsController,
    sharedMonitoringHistoryController,
} from '@controllers/monitoring-details';
import { panelController } from '@controllers/panel';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import {
    type MonitoringDataType,
    sharedWorkspaceMonitorsCoordinatorController,
} from '@controllers/workspace-monitors-coordinator';
import { ActionStack } from '@cosmos/components/action-stack';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { MonitoringDetailsPanelComponent } from '../components/monitor-details-panel.component';
import {
    getBulkActionDropdownItems,
    getMonitoringActions,
} from '../constants/actions.constant';
import { getControlsMonitoringColumns } from '../constants/columns.constant';
import type { ControlMonitoringType } from '../types/controls-monitoring.type';

const convertToControlMonitoringType = (
    monitoringData: MonitoringDataType[],
): ControlMonitoringType[] => {
    return monitoringData.map((item) => {
        return {
            name: item.testName,
            checkResultStatus: item.checkResultStatus,
            checkTypes: item.checkTypes,
            findings: '--',
            availableConnections: item.availableConnections ?? [],
            draft: item.draft,
            testId: item.testId,
        };
    });
};

const openMonitoringPanel = action((testId: number): void => {
    sharedWorkspaceMonitorsController.loadWorkspaceMonitorTestOverview(testId);
    activeTrackCardController.loadTrackData(testId);
    sharedMonitorFindingsController.loadFindingsResponse(testId);
    activeTicketsMetadataController.loadTicketsMetadata(testId);
    sharedMonitoringHistoryController.loadMonitoringHistory(testId);
    sharedMonitoringDetailsControlsController.loadMonitoringDetailsControls(
        testId,
    );

    panelController.openPanel({
        id: 'monitoring-panel',
        content: () => <MonitoringDetailsPanelComponent data-id="r4-3_HsY" />,
    });
});

export const ControlsMonitoringView = observer((): React.JSX.Element => {
    const { combinedMonitoringData, isLoading } =
        sharedWorkspaceMonitorsCoordinatorController;

    const monitoringData = convertToControlMonitoringType(
        combinedMonitoringData,
    );

    return (
        <Stack gap="4x" direction="column" data-id="36rgNcba" align="end">
            <ActionStack
                isFullWidth={false}
                stacks={[
                    {
                        id: 'asdf',
                        actions: getMonitoringActions(),
                        alignment: 'left',
                    },
                ]}
            />
            <AppDatatable
                tableId="datatable-monitoring-list"
                data-testid="ControlsMonitoringView"
                data-id="cLwGEtjx"
                isLoading={isLoading}
                total={monitoringData.length}
                data={monitoringData}
                columns={getControlsMonitoringColumns()}
                bulkActionDropdownItems={getBulkActionDropdownItems()}
                tableSearchProps={{
                    hideSearch: true,
                }}
                emptyStateProps={{
                    title: t`Continuously monitor the effectiveness of your control`,
                    description: t`See a history of how tests mapped to this control have performed over time.`,
                    illustrationName: 'MonitoringTest',
                    rightAction: (
                        <Button
                            label={t`Compare to defaults`}
                            colorScheme="neutral"
                            level="secondary"
                            size="md"
                        />
                    ),
                    leftAction: (
                        <Button
                            label={t`Map tests`}
                            colorScheme="primary"
                            size="md"
                        />
                    ),
                }}
                onRowClick={({ row }) => {
                    openMonitoringPanel(row.testId);
                }}
                onFetchData={(params) => {
                    sharedWorkspaceMonitorsCoordinatorController.loadMonitors(
                        params,
                    );
                }}
            />
        </Stack>
    );
});
