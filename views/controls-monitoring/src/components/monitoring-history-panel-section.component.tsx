import { sharedMonitoringHistoryController } from '@controllers/monitoring-details';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import {
    dataCategorizeGreen2,
    dataCategorizeRed2,
    dataCategorizeYellow2,
    dataEmphasizeNeutralModerate,
} from '@cosmos/constants/tokens';
import { DataBar } from '@cosmos-lab/components/data-bar';
import { DataLegend } from '@cosmos-lab/components/data-legend';
import { PanelSection } from '@cosmos-lab/components/panel-section';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringDetailsModel } from '../models/monitoring-details.model';

export const MonitoringHistoryPanelSectionComponent = observer(
    (): React.JSX.Element => {
        const { history } = sharedMonitoringDetailsModel;
        const { isLoading } = sharedMonitoringHistoryController;

        if (isLoading) {
            return <Skeleton barCount={3} />;
        }

        return (
            <PanelSection
                showBorderBottom
                data-id="monitoring-history-panel-section"
                data-testid="MonitoringHistoryPanelSectionComponent"
                title={t`Test History`}
                body={
                    <Stack gap="2xl" direction={'column'}>
                        <DataLegend
                            data-id="yxDO"
                            direction="row"
                            position="bottom"
                            data-testid="HistoryTooltip"
                            data={[
                                {
                                    label: t`Pass`,
                                    value: 0,
                                    color: dataCategorizeGreen2,
                                },
                                {
                                    label: t`Fail`,
                                    value: 0,
                                    color: dataCategorizeRed2,
                                },
                                {
                                    label: t`Error`,
                                    value: 0,
                                    color: dataCategorizeYellow2,
                                },
                                {
                                    label: t`None`,
                                    value: 0,
                                    color: dataEmphasizeNeutralModerate,
                                },
                            ]}
                        >
                            {' '}
                        </DataLegend>
                        <DataBar
                            categoryKey="month"
                            height={200}
                            data-id="jQuC7YEL"
                            data={history}
                            bars={[
                                {
                                    dataKey: 'success',
                                    fill: dataCategorizeGreen2,
                                    barSize: 20,
                                },
                                {
                                    dataKey: 'failed',
                                    fill: dataCategorizeRed2,
                                    barSize: 20,
                                },
                                {
                                    dataKey: 'error',
                                    fill: dataCategorizeYellow2,
                                    barSize: 20,
                                },
                            ]}
                        />
                    </Stack>
                }
            />
        );
    },
);
