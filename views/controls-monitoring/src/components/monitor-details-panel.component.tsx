import { useCallback } from 'react';
import { activeTrackCardController } from '@controllers/monitoring-details';
import { panelController } from '@controllers/panel';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import { Metadata } from '@cosmos/components/metadata';
import {
    PanelBody,
    PanelControls,
    PanelHeader,
} from '@cosmos/components/panel';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringDetailsModel } from '../models/monitoring-details.model';
import { sharedMonitoringDetailsPanelModel } from '../models/monitoring-details-panel.model';
import { MonitoringControlsPanelSectionComponent } from './monitoring-controls-panel-section';
import { MonitoringDetailsPanelSectionComponent } from './monitoring-details-panel-section.component';
import { MonitoringHistoryPanelSectionComponent } from './monitoring-history-panel-section.component';
import { MonitoringOverviewPanelSectionComponent } from './monitoring-overview-panel-section.component';

export const MonitoringDetailsPanelComponent = observer(
    (): React.JSX.Element => {
        const {
            workspaceMonitorTestOverview: monitoringDetails,
            isOverviewLoading,
        } = sharedWorkspaceMonitorsController;
        const { isLoading: isTrackLoading } = activeTrackCardController;
        const currentWorkspaceId =
            sharedWorkspacesController.currentWorkspace?.id;
        const { status } = sharedMonitoringDetailsModel;

        const {
            currentMonitoringIndex,
            monitoringsDetails,
            onNextPageClick,
            onPrevPageClick,
        } = sharedMonitoringDetailsPanelModel;

        const closePanel = useCallback(() => {
            panelController.closePanel();
        }, []);

        if (isOverviewLoading || !monitoringDetails || isTrackLoading) {
            return <Skeleton barCount={5} />;
        }

        return (
            <>
                <PanelControls
                    closeButtonLabel={t`Close`}
                    data-id="monitoring-control-panel-controls"
                    pagination={{
                        currentItem: currentMonitoringIndex + 1,
                        onNextPageClick,
                        onPrevPageClick,
                        totalItems: monitoringsDetails.length,
                    }}
                    onClose={closePanel}
                />
                <PanelHeader
                    data-id="monitoring-control-panel-header"
                    title={monitoringDetails.name}
                    openPageLink={`/workspaces/${currentWorkspaceId}/compliance/monitoring/monitors/${monitoringDetails.testId}/overview`}
                    slot={
                        status ? (
                            <Metadata
                                type="status"
                                label={status.label}
                                colorScheme={status.colorScheme}
                                data-id="L5NtxzdR"
                                data-testid="MonitorResultCell"
                            />
                        ) : (
                            <EmptyValue label={t`Status`} />
                        )
                    }
                />
                <PanelBody data-id="monitoring-control-panel-body">
                    <Stack gap="4x" direction="column">
                        <MonitoringOverviewPanelSectionComponent />

                        <MonitoringDetailsPanelSectionComponent />

                        <MonitoringHistoryPanelSectionComponent />

                        <MonitoringControlsPanelSectionComponent />
                    </Stack>
                </PanelBody>
            </>
        );
    },
);
