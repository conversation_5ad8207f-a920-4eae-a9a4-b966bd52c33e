import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { PanelSection } from '@cosmos-lab/components/panel-section';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const MonitoringDetailsPanelSectionComponent = observer(
    (): React.JSX.Element => {
        const {
            workspaceMonitorTestOverview: monitoringDetails,
            isOverviewLoading,
        } = sharedWorkspaceMonitorsController;

        if (!monitoringDetails || isOverviewLoading) {
            return <Skeleton barCount={3} />;
        }

        return (
            <PanelSection
                showBorderBottom
                title={t`Test Details`}
                data-id="aWlWjPOC"
                body={
                    <Stack gap="2xl" direction={'column'}>
                        <KeyValuePair
                            label={t`Description`}
                            value={monitoringDetails.description}
                        />
                        <KeyValuePair
                            label={t`Test type`}
                            value={monitoringDetails.source}
                        />
                        <KeyValuePair
                            label={t`Status`}
                            value={monitoringDetails.checkStatus}
                        />
                    </Stack>
                }
            />
        );
    },
);
