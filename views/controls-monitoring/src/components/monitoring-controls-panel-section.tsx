import { MappedControlsList } from '@components/mapped-controls-list';
import { sharedMonitoringDetailsControlsController } from '@controllers/monitoring-details';
import { PanelSection } from '@cosmos-lab/components/panel-section';
import { observer } from '@globals/mobx';

export const MonitoringControlsPanelSectionComponent = observer(
    (): React.JSX.Element => {
        const {
            monitoringDetailsControlsData: controls,
            isLoading: isControlsLoading,
        } = sharedMonitoringDetailsControlsController;

        return (
            <PanelSection
                data-id="monitoring-controls-panel-section"
                data-testid="MonitoringControlsPanelSectionComponent"
                title={''}
                body={
                    <MappedControlsList
                        controls={controls}
                        isLoading={isControlsLoading}
                    />
                }
            />
        );
    },
);
