import { Stack } from '@cosmos/components/stack';
import { PanelSection } from '@cosmos-lab/components/panel-section';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringDetailsModel } from '../models/monitoring-details.model';

export const MonitoringOverviewPanelSectionComponent = observer(
    (): React.JSX.Element => {
        const {
            numberOfDays,
            sinceDateText,
            statusLabel,
            statusColor,
            resourcesFailingCount,
            resourcesFailingText,
            ticketsInProgress,
            totalTicketsText,
        } = sharedMonitoringDetailsModel;

        return (
            <PanelSection
                showBorderBottom
                data-id="monitoring-overview-panel-section"
                data-testid="MonitoringOverviewPanelSectionComponent"
                title={t`Overview`}
                body={
                    <Stack direction="row" gap="2xl">
                        <StatBlock
                            title={statusLabel}
                            statValue={numberOfDays}
                            statIconColor={statusColor}
                            statValueColor={statusColor}
                            state="static"
                            statIcon="Calendar"
                            trendDirection="down"
                            trendSentiment="negative"
                            totalText={sinceDateText}
                            showTrendBlock={false}
                        />
                        <StatBlock
                            title={t`Failing resources`}
                            statValue={resourcesFailingCount}
                            state="static"
                            statIcon="Cancel"
                            trendDirection="down"
                            trendSentiment="negative"
                            totalText={resourcesFailingText}
                            showTrendBlock={false}
                            statIconColor={
                                resourcesFailingCount > 0
                                    ? 'critical'
                                    : 'neutral'
                            }
                            statValueColor={
                                resourcesFailingCount > 0
                                    ? 'critical'
                                    : 'neutral'
                            }
                        />
                        <StatBlock
                            title={t`Tickets`}
                            statValue={ticketsInProgress}
                            state="static"
                            statIcon="Cancel"
                            trendDirection="down"
                            trendSentiment="negative"
                            totalText={totalTicketsText}
                            statIconColor="neutral"
                            statValueColor="neutral"
                            showTrendBlock={false}
                        />
                    </Stack>
                }
            />
        );
    },
);
