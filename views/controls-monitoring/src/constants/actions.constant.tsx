import type { BulkAction, TableAction } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';

export const getMonitoringActions = (): TableAction[] =>
    [
        {
            actionType: 'button',
            id: 'compare-button',
            typeProps: {
                level: 'tertiary',
                label: t`Compare to defaults`,
            },
        },
        {
            actionType: 'button',
            id: 'map-requirements-button',
            typeProps: {
                level: 'secondary',
                label: t`Map tests`,
            },
        },
    ] as const satisfies TableAction[];

export const getBulkActionDropdownItems = (): BulkAction[] =>
    [
        {
            actionType: 'button',
            id: 'exclude-monitoring-button',
            typeProps: {
                label: t`Exclude`,
                level: 'tertiary',
            },
        },
        {
            actionType: 'button',
            id: 'disable-monitoring-button',
            typeProps: {
                label: t`Disable`,
                level: 'tertiary',
            },
        },
    ] as const satisfies BulkAction[];
