import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { MonitorCategoryCell } from '../components/monitor-category-cell.component';
import { MonitorConnectionCell } from '../components/monitor-connection-cell.component';
import { MonitorNameCell } from '../components/monitor-name-cell.component';
import { MonitorResultCell } from '../components/monitor-result-cell.component';
import type { ControlMonitoringType } from '../types/controls-monitoring.type';

export const getControlsMonitoringColumns =
    (): DatatableProps<ControlMonitoringType>['columns'] => {
        return [
            {
                accessorKey: 'name',
                header: t`Name`,
                id: 'name',
                enableSorting: true,
                cell: MonitorNameCell,
            },
            {
                accessorKey: 'checkResultStatus',
                header: t`Results`,
                id: 'result',
                enableSorting: true,
                cell: MonitorResultCell,
            },
            {
                header: t`Finding`,
                id: 'finding',
                accessorKey: 'finding',
                enableSorting: true,
            },
            {
                header: t`Category`,
                id: 'checkTypes',
                accessor<PERSON>ey: 'checkTypes',
                enableSorting: true,
                cell: MonitorCategoryCell,
            },
            {
                accessorKey: 'availableConnections',
                header: t`Connection`,
                id: 'connection',
                enableSorting: true,
                cell: MonitorConnectionCell,
            },
        ];
    };
