import {
    activeTicketsMetadata<PERSON>ontroller,
    activeTrackCardController,
    sharedMonitorFindingsController,
    sharedMonitoringDetailsControlsController,
    sharedMonitoringHistoryController,
} from '@controllers/monitoring-details';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import {
    type MonitoringDataType,
    sharedWorkspaceMonitorsCoordinatorController,
} from '@controllers/workspace-monitors-coordinator';
import { makeAutoObservable } from '@globals/mobx';

class MonitoringDetailsPanelModel {
    constructor() {
        makeAutoObservable(this);
    }

    loadPanelInfo = (testId: number) => {
        sharedWorkspaceMonitorsController.loadWorkspaceMonitorTestOverview(
            testId,
        );
        activeTrackCardController.loadTrackData(testId);
        sharedMonitorFindingsController.loadFindingsResponse(testId);
        activeTicketsMetadataController.loadTicketsMetadata(testId);
        sharedMonitoringHistoryController.loadMonitoringHistory(testId);
        sharedMonitoringDetailsControlsController.loadMonitoringDetailsControls(
            testId,
        );
    };

    get monitoringsDetails(): MonitoringDataType[] {
        const { combinedMonitoringData } =
            sharedWorkspaceMonitorsCoordinatorController;

        return combinedMonitoringData;
    }

    get currentMonitoringIndex(): number {
        return this.monitoringsDetails.findIndex(
            (monitoring) =>
                monitoring.testId ===
                sharedWorkspaceMonitorsController.workspaceMonitorTestOverview
                    ?.testId,
        );
    }

    onNextPageClick = () => {
        if (
            this.currentMonitoringIndex ===
            this.monitoringsDetails.length - 1
        ) {
            return;
        }
        const nextMonitoring =
            this.monitoringsDetails[this.currentMonitoringIndex + 1];

        this.loadPanelInfo(nextMonitoring.testId);
    };

    onPrevPageClick = () => {
        if (this.currentMonitoringIndex === 0) {
            return;
        }
        const prevMonitoring =
            this.monitoringsDetails[this.currentMonitoringIndex - 1];

        this.loadPanelInfo(prevMonitoring.testId);
    };
}

export const sharedMonitoringDetailsPanelModel =
    new MonitoringDetailsPanelModel();
