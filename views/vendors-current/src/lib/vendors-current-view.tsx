import { useCallback, useState } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedVendorsCurrentController } from '@controllers/vendors';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedVendorsCurrentModel } from '@models/vendors-profile';
import { useNavigate } from '@remix-run/react';
import { getVendorsCurrentColumns } from './vendors-current-table-columns';
import {
    getVendorsCurrentTableActions,
    VENDORS_CURRENT_DEFAULT_SORTING,
} from './vendors-current-table-config';

export const VendorsCurrentView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const { allVendorsCurrent, loadVendorsCurrent, isLoading } =
        sharedVendorsCurrentController;

    const [tableParams, setTableParams] = useState<
        FetchDataResponseParams | undefined
    >();

    const handleFetchData = useCallback(
        (params: FetchDataResponseParams): void => {
            setTableParams(params);
            loadVendorsCurrent(params);
        },
        [loadVendorsCurrent],
    );

    return (
        <AppDatatable
            // isRowSelectionEnabled
            isFullPageTable
            isLoading={isLoading}
            tableId="datatable-vendors-current"
            data={allVendorsCurrent.data}
            data-id="datatable-vendors-current"
            columns={getVendorsCurrentColumns()}
            total={allVendorsCurrent.total}
            filterProps={sharedVendorsCurrentModel.filters}
            tableActions={getVendorsCurrentTableActions(tableParams)}
            initialSorting={VENDORS_CURRENT_DEFAULT_SORTING}
            tableSearchProps={{
                placeholder: t`Search`,
            }}
            defaultPaginationOptions={{
                pageIndex: 0,
                pageSize: DEFAULT_PAGE_SIZE,
                pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
            }}
            onFetchData={handleFetchData}
            onRowClick={({ row }) => {
                navigate(`${row.id}/overview`);
            }}
        />
    );
});
