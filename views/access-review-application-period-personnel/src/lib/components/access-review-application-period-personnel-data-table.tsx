import { isEmpty, isNil, isObject } from 'lodash-es';
import { useCallback, useMemo, useState } from 'react';
import { AppDatatable } from '@components/app-datatable';
import {
    sharedAccessReviewApplicationGroupsController,
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationSummaryController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { activeAccessReviewsApplicationsUserController } from '@controllers/access-reviews-applications';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { UserAccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { createAccessReviewPeriodFiltersModel } from '@models/access-review';
import { useNavigate } from '@remix-run/react';
import { accessReviewPersonnelAdapter } from '../adapters/access-review-personnel.adapter';
import { getColumns } from '../constants/access-review-application-period-personnel-data-table-columns.constants';
import { sharedAccessReviewPersonnelTableModel } from '../models/access-review-personnel-table.model';

// Make sure the component is wrapped with observer
export const AccessReviewApplicationPeriodPersonnelDataTable = observer(
    (): React.JSX.Element => {
        const [isSummaryView, setIsSummaryView] = useState(true);
        const navigate = useNavigate();

        // =========================================================================
        // Controller data
        // =========================================================================
        const {
            isLoading,
            periodId,
            applicationId,
            accessReviewPeriodApplicationUsersList,
            totalReviewApplicationUsers,
            loadAccessReviewPeriodApplicationUsers,
            updateFilterValues,
        } = sharedAccessReviewPeriodApplicationUsersController;

        const { accessReviewPeriodApplicationSummaryDetails } =
            sharedAccessReviewPeriodApplicationSummaryController;

        const { accessReviewApplicationConnections } =
            activeAccessReviewsApplicationsUserController;

        // Create a non-reactive wrapper to avoid MobX tracking issues
        const loadAccessReviewApplicationUsersConnections = useCallback(() => {
            activeAccessReviewsApplicationsUserController.loadAccessReviewApplicationUsersConnections();
        }, []);

        const { currentWorkspace, isLoading: isWorkspaceLoading } =
            sharedWorkspacesController;

        const {
            isManuallyAddedApplication,
            accessReviewPeriodApplicationDetails,
            isLoading: isLoadingPeriodApplication,
        } = sharedAccessReviewPeriodApplicationController;

        const { hasLimitedAccess } = sharedFeatureAccessModel;

        const isLoadingData =
            isLoading || isLoadingPeriodApplication || isWorkspaceLoading;

        const { application } = accessReviewPeriodApplicationDetails ?? {};

        const { clientType, source } = application ?? {};

        const workspaceId = currentWorkspace?.id ?? 1;

        // =========================================================================
        // Filters model
        // =========================================================================
        const {
            applicationGroups,
            hasNextPage,
            isLoading: isLoadingGroups,
        } = sharedAccessReviewApplicationGroupsController;

        const filtersModel = useMemo(() => {
            return createAccessReviewPeriodFiltersModel({
                clientType,
                source,
                isManuallyAddedApplication,
                accessReviewApplicationGroupsList: applicationGroups,
                loadAccessReviewApplicationGroupsForPersonnel: (params) => {
                    sharedAccessReviewApplicationGroupsController.loadApplicationGroups(
                        params,
                    );
                },
                accessReviewApplicationConnections,
                loadAccessReviewApplicationUsersConnections,
                updateFilterValues,
                accessReviewPeriodApplicationSummaryDetails,
                hasMoreGroups: hasNextPage,
                isLoadingGroups,
            });
        }, [
            clientType,
            source,
            isManuallyAddedApplication,
            applicationGroups,
            accessReviewApplicationConnections,
            loadAccessReviewApplicationUsersConnections,
            updateFilterValues,
            accessReviewPeriodApplicationSummaryDetails,
            hasNextPage,
            isLoadingGroups,
        ]);

        const { warningFiltersObject, tableFilters, filterValues } =
            filtersModel;

        // Check if any filters are currently active
        const hasActiveFilters = useMemo(() => {
            return Object.values(filterValues).some((filter) => {
                const { value } = filter;

                if (Array.isArray(value)) {
                    if (isEmpty(value)) {
                        return false;
                    }

                    return !value.every((item: unknown) => {
                        const itemValue =
                            isObject(item) && 'value' in item
                                ? (item as { value: unknown }).value
                                : item;

                        return (
                            itemValue === 'ALL_CURRENT_PERSONNEL' ||
                            itemValue === 'ALL_PERSONNEL'
                        );
                    });
                }

                if (isNil(value) || value === '') {
                    return false;
                }

                const valueToCheck =
                    isObject(value) && 'value' in value
                        ? (value as { value: unknown }).value
                        : value;
                const stringValue = String(valueToCheck);
                const isDefaultAllValue =
                    stringValue === 'ALL_REVIEW_STATUS' ||
                    stringValue === 'ALL_PERMISSIONS' ||
                    stringValue === 'ALL_CURRENT_PERSONNEL' ||
                    stringValue === 'ALL_PERSONNEL';

                return !isDefaultAllValue;
            });
        }, [filterValues]);

        // =========================================================================
        // Event handlers
        // =========================================================================
        /**
         * Handles changing between summary and detailed views.
         */
        const handleSummaryViewChange = useCallback((value: boolean) => {
            setIsSummaryView(value);
        }, []);
        /**
         * Handles row click to navigate to personnel details.
         */
        const handleRowClick = useCallback(
            ({
                row,
            }: {
                row: UserAccessReviewPeriodApplicationResponseDto;
            }) => {
                navigate(
                    `/workspaces/${workspaceId}/governance/access-review/active/${applicationId}/period/${periodId}/personnel/${row.applicationUserId}`,
                );
            },
            [navigate, workspaceId, applicationId, periodId],
        );

        /**
         * Handles data fetching when table parameters change.
         */
        const handleFetchData = useCallback(
            (fetchParams: FetchDataResponseParams | null) => {
                if (!fetchParams) {
                    return;
                }

                filtersModel.handleFilterChange(fetchParams);

                loadAccessReviewPeriodApplicationUsers(fetchParams);
            },
            [loadAccessReviewPeriodApplicationUsers, filtersModel],
        );

        // =========================================================================
        // Memoized values
        // =========================================================================
        const filteredColumns = useMemo(
            () =>
                getColumns().filter((column) => {
                    return column.showIf({
                        isSummaryView,
                        isManuallyAddedApplication,
                        hasLimitedAccess,
                        missingMFA:
                            warningFiltersObject.warningFilters.missingMFA,
                    });
                }),
            [
                isSummaryView,
                isManuallyAddedApplication,
                warningFiltersObject.warningFilters.missingMFA,
                hasLimitedAccess,
            ],
        );

        // Use the adapter to transform data for the view
        const {
            tableData,
            totalCount,
            tableSettingsTriggerProps,
            tableSearchProps,
            emptyStateProps,
            filterViewModeProps,
        } = useMemo(
            () =>
                accessReviewPersonnelAdapter({
                    accessReviewPeriodApplicationUsersList,
                    totalReviewApplicationUsers,
                    isSummaryView,
                    application,
                    handleSummaryViewChange,
                    hasActiveFilters,
                }),
            [
                accessReviewPeriodApplicationUsersList,
                totalReviewApplicationUsers,
                isSummaryView,
                application,
                handleSummaryViewChange,
                hasActiveFilters,
            ],
        );
        const { bulkActions, handleRowSelection } =
            sharedAccessReviewPersonnelTableModel;

        // Create a unique table ID and key based on application, period, and filter configuration
        // This forces a complete remount when switching between different contexts
        const uniqueTableId = `personnel-period-datatable-${applicationId}-${periodId}-${clientType}`;
        const filterConfigKey = tableFilters.filters
            .map((f) => f.id)
            .sort()
            .join('-');
        const uniqueKey = `${uniqueTableId}-${filterConfigKey}`;

        return (
            <AppDatatable
                // isRowSelectionEnabled
                isSortable
                isFullPageTable
                key={uniqueKey}
                isLoading={isLoadingData}
                tableId={uniqueTableId}
                data-id="personnel-period-datatable"
                data-testid="AccessReviewApplicationPeriodPersonnelDataTable"
                columns={filteredColumns}
                total={totalCount}
                filterProps={tableFilters}
                data={tableData}
                tableSettingsTriggerProps={tableSettingsTriggerProps}
                emptyStateProps={emptyStateProps}
                tableSearchProps={tableSearchProps}
                filterViewModeProps={filterViewModeProps}
                bulkActionDropdownItems={bulkActions}
                onFetchData={handleFetchData}
                onRowClick={handleRowClick}
                onRowSelection={handleRowSelection}
            />
        );
    },
);
