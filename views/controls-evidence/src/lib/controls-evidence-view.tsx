import { handleOpenEvidencePanel } from '@components/evidence-library';
import { sharedControlEvidenceController } from '@controllers/controls';
import { Datatable } from '@cosmos/components/datatable';
import { observer } from '@globals/mobx';
import { CONTROLS_EVIDENCE_COLUMNS } from '../constants/columns.constant';

export const ControlsEvidenceView = observer((): React.JSX.Element => {
    const { isLoading, total, controlEvidence, loadEvidenceUnionPage } =
        sharedControlEvidenceController;

    return (
        <Datatable
            isFullPageTable
            // isRowSelectionEnabled
            isLoading={isLoading}
            tableId="datatable-control-evidence-list"
            total={total}
            data={controlEvidence}
            columns={CONTROLS_EVIDENCE_COLUMNS}
            data-testid="ControlsEvidenceView"
            data-id="XOpVf3JI"
            emptyStateProps={{
                illustrationName: 'Warning',
                title: 'Evidence',
                description: 'No evidence was found',
            }}
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        isIconOnly: true,
                        label: 'Download',
                        level: 'secondary',
                    },
                },
                {
                    actionType: 'button',
                    id: 'add-evidence-button',
                    typeProps: {
                        label: 'Add evidence',
                        level: 'secondary',
                    },
                },
            ]}
            onFetchData={loadEvidenceUnionPage}
            onRowClick={({ row }) => {
                handleOpenEvidencePanel(row.evidenceId);
            }}
        />
    );
});
