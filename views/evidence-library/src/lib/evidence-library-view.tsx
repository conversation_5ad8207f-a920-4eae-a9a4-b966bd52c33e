import { isNil } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import { sharedEvidenceLibraryListController } from '@controllers/evidence-library';
import type { EvidenceResponseDto } from '@globals/api-sdk/types';
// import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { getEvidenceLibraryColumns } from '../helpers/get-evidence-library-columns.helper';
import { EvidenceLibraryBulkActionsModel } from '../models/evidence-library-bulk-actions.model';
import { EvidenceLibraryFiltersModel } from '../models/evidence-library-filters.model';

export const EvidenceLibraryView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const { currentWorkspace } = sharedWorkspacesController;
    // const { hasWriteEvidenceLibraryPermission } = sharedFeatureAccessModel;

    const navigateToEvidenceDetail = ({
        row: evidence,
    }: {
        row: EvidenceResponseDto;
    }) => {
        if (isNil(currentWorkspace)) {
            return;
        }

        navigate(`${evidence.id}/overview`);
    };

    const { evidenceList, isLoading, total, loadPage } =
        sharedEvidenceLibraryListController;

    const { filters } = new EvidenceLibraryFiltersModel();
    const { bulkActions, handleRowSelection } =
        new EvidenceLibraryBulkActionsModel();

    return (
        <AppDatatable
            isFullPageTable
            // isRowSelectionEnabled={hasWriteEvidenceLibraryPermission}
            isLoading={isLoading}
            tableId="datatable-evidence-list"
            columns={getEvidenceLibraryColumns()}
            filterProps={filters}
            total={total}
            data-testid="EvidenceLibraryView"
            data-id="-UF9rGDd"
            data={evidenceList}
            bulkActionDropdownItems={bulkActions}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: t`Evidence`,
                description: t`No evidence was found`,
            }}
            tableSearchProps={{
                placeholder: t`Search evidence by name or control`,
            }}
            onRowSelection={handleRowSelection}
            onFetchData={loadPage}
            onRowClick={navigateToEvidenceDetail}
        />
    );
});
