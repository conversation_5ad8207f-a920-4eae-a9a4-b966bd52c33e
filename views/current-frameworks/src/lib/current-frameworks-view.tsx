import { useCallback, useMemo } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { FrameworksReadinessToggleComponent } from '@components/frameworks';
import { sharedFrameworksController } from '@controllers/frameworks';
import type {
    DatatableProps,
    FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { useLingui } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { useLocation, useNavigate } from '@remix-run/react';
import { FrameworkGalleryCard } from '../components/framework-gallery-card';

const COLUMNS =
    [] as const satisfies DatatableProps<FrameworkResponseDto>['columns'];

const TABLE_SEARCH_PROPS = {
    hideSearch: true,
};

const handleFetchData = action((param: FetchDataResponseParams) => {
    const page = Number(param.pagination.page) || 1;
    const limit = Number(param.pagination.pageSize);

    sharedFrameworksController.loadPage(page, limit);
});

export const CurrentFrameworksView = observer((): React.JSX.Element => {
    const { t } = useLingui();
    const navigate = useNavigate();
    const location = useLocation();

    const { productFrameworks, isLoading, frameworksTotal } =
        sharedFrameworksController;

    const handleRowClick = useCallback(
        ({ row }: { row: FrameworkResponseDto }) => {
            navigate(`${location.pathname}/${row.id}/details`);
        },
        [location.pathname, navigate],
    );

    const emptyStateProps = useMemo(
        () =>
            ({
                illustrationName: 'Warning',
                title: t`Frameworks`,
                description: t`No frameworks were found`,
            }) as const,
        [t],
    );

    return (
        <Stack direction="column" gap="4x" data-id="hFE6A90F">
            <FrameworksReadinessToggleComponent />

            <AppDatatable
                isFullPageTable
                viewMode="gallery"
                columns={COLUMNS}
                isLoading={isLoading}
                tableId="datatable-frameworks"
                total={frameworksTotal}
                data={productFrameworks}
                galleryCard={FrameworkGalleryCard}
                tableSearchProps={TABLE_SEARCH_PROPS}
                emptyStateProps={emptyStateProps}
                onRowClick={handleRowClick}
                onFetchData={handleFetchData}
            />
        </Stack>
    );
});
