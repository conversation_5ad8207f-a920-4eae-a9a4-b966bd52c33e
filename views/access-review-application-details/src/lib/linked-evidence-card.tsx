import { activeAccessReviewApplicationDetailsController } from '@controllers/access-reviews';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Datatable, type DatatableProps } from '@cosmos/components/datatable';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { AccessReviewPeriodApplicationEvidenceResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { openAccessReviewUploadEvidenceModal } from './helpers/access-review-open-upload-evidence-modal';
import { ActionsCell } from './linked-evidence-table-cells/actions-cell';
import { CreationDateCell } from './linked-evidence-table-cells/creation-date-cell';
import { FilenameCell } from './linked-evidence-table-cells/filename-cell';

export const LinkedEvidenceCard = observer((): React.JSX.Element => {
    const {
        isLoading,
        isApplicationDisabled,
        applicationEvidences,
        showLinkedEvidence,
    } = activeAccessReviewApplicationDetailsController;
    const { hasLimitedAccess } = sharedFeatureAccessModel;

    const LINKED_EVIDENCE_COLUMNS: DatatableProps<AccessReviewPeriodApplicationEvidenceResponseDto>['columns'] =
        [
            {
                header: t`File name`,
                id: 'file-name',
                accessorKey: 'name',
                cell: FilenameCell,
                size: 500,
                enableSorting: false,
            },
            {
                header: t`Creation date`,
                accessorKey: 'currentVersion',
                id: 'creation-date',
                cell: CreationDateCell,
                enableSorting: false,
            },
            {
                header: '',
                accessorKey: 'id',
                id: 'actions',
                cell: ActionsCell,
                enableSorting: false,
            },
        ];

    if (isLoading) {
        return <Skeleton barCount={1} />;
    }

    return (
        <Box data-id="linked-evidence-card" height="fit-content">
            <Card
                size="lg"
                title={t`Linked evidence`}
                data-testid="LinkedEvidenceCard"
                body={
                    <Stack direction="column" gap="xl">
                        {isApplicationDisabled || (
                            <Text>
                                {t`Link evidence to show you've performed a user access review on this application.`}
                            </Text>
                        )}
                        {showLinkedEvidence && (
                            <Datatable
                                hidePagination
                                isLoading={false}
                                tableId="datatable-linked-evidence"
                                data={applicationEvidences}
                                columns={LINKED_EVIDENCE_COLUMNS}
                                data-id="datatable-linked-evidence"
                                total={applicationEvidences.length}
                                tableSearchProps={{
                                    hideSearch: true,
                                }}
                            />
                        )}
                    </Stack>
                }
                actions={
                    isApplicationDisabled || hasLimitedAccess
                        ? []
                        : [
                              {
                                  actionType: 'button',
                                  id: 'add-button',
                                  typeProps: {
                                      label: t`Add evidence`,
                                      level: 'secondary',
                                      'data-id': 'add-button',
                                      onClick:
                                          openAccessReviewUploadEvidenceModal,
                                  },
                              },
                          ]
                }
            />
        </Box>
    );
});
