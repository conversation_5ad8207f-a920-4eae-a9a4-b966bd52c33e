import { AppDatatable } from '@components/app-datatable';
import { sharedRiskLibraryController } from '@controllers/risk';
import { observer } from '@globals/mobx';
import {
    RISK_LIBRARY_COLUMNS,
    RISK_LIBRARY_FILTERS,
} from './constants/risk-register-library.constants';

export const RiskRegisterLibraryView = observer((): React.JSX.Element => {
    const { risks, isLoading, loadRiskLibrary, total } =
        sharedRiskLibraryController;

    return (
        <AppDatatable
            // isRowSelectionEnabled
            isFullPageTable
            isLoading={isLoading}
            tableId="risk-register-library-datatable"
            total={total}
            data={risks}
            columns={RISK_LIBRARY_COLUMNS}
            filterProps={RISK_LIBRARY_FILTERS}
            data-testid="RiskRegisterLibraryView"
            data-id="XWGtoM9e"
            tableSearchProps={{
                placeholder:
                    'Search by name, description, control code, or requirement ',
                hideSearch: false,
                debounceDelay: 1000,
                defaultValue: '',
            }}
            onFetchData={loadRiskLibrary}
        />
    );
});
