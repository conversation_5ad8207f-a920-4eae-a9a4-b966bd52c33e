import { useMemo } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { VendorsRisksOverviewPanelComponent } from '@components/vendors-risks-overview-panel';
import {
    handleOpenDetailsPanel,
    sharedVendorsRisksController,
    sharedVendorsRisksDownloadController,
    VENDORS_RISKS_FILTER_RISK_POSTURE_ID,
    VENDORS_RISKS_FILTER_STATUS_ID,
} from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { DataPosture } from '@cosmos-lab/components/data-posture';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { plural, t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { generateRiskPostureBoxes } from '@helpers/risk-score';
import { useNavigate } from '@remix-run/react';
import { DEFAULT_PAGINATION_OPTIONS } from './constants/vendors-risks-view.constants';
import { getPostureValueFromThresholdId } from './helpers/get-posture-value-from-threshold-id.helper';
import {
    getTableActions,
    getTableColumns,
    getVendorRisksFilters,
} from './helpers/vendors-risks.helpers';

export const VendorsRisksView = observer((): React.JSX.Element => {
    const {
        settings,
        risks,
        isLoading,
        loadRisks,
        dashboard,
        risksQuery,
        hasFilter,
        isSettingsLoading,
    } = sharedVendorsRisksController;

    const { isLoading: isDownloadLoading, downloadVendorRisksReport } =
        sharedVendorsRisksDownloadController;

    const navigate = useNavigate();
    const remainingCount = dashboard?.remaining ?? 0;

    const riskPostureBoxes = useMemo(() => {
        const baseBoxes = generateRiskPostureBoxes(
            dashboard?.riskPosture ?? {},
            settings?.thresholds ?? [],
        );

        return baseBoxes.map((box) => ({
            ...box,
            onClick: () => {
                const postureValue = getPostureValueFromThresholdId(
                    box.id,
                    settings?.thresholds ?? [],
                );

                // Navigate with URL parameters for risk posture and ACTIVE status
                const searchParams = new URLSearchParams();

                searchParams.set(
                    VENDORS_RISKS_FILTER_RISK_POSTURE_ID,
                    postureValue,
                );
                searchParams.set(VENDORS_RISKS_FILTER_STATUS_ID, 'ACTIVE');

                navigate(`?${searchParams.toString()}`);
            },
        }));
    }, [dashboard?.riskPosture, settings?.thresholds, navigate]);

    if (
        !isLoading &&
        !isSettingsLoading &&
        risksQuery.data?.total === 0 &&
        !hasFilter
    ) {
        return (
            <EmptyState
                title={t`See all risks related to your vendors in one place.`}
                description={t`When you add a risk to a vendor, it’ll show up here.`}
                illustrationName="RiskManagement"
            />
        );
    }

    return (
        <>
            {isSettingsLoading ? (
                <Loader isSpinnerOnly label={t`Loading...`} />
            ) : (
                <Grid
                    columns="2fr 6fr"
                    gap="4x"
                    pb="4x"
                    data-testid="VendorsRisksView"
                >
                    <Box
                        borderRadius="borderRadiusLg"
                        borderColor="neutralBorderFaded"
                        borderWidth="borderWidth1"
                        p="xl"
                    >
                        <StatBlock
                            title={t`Risk assessed`}
                            statValue={dashboard?.scored ?? 0}
                            statValueColor="neutral"
                            totalText={t`${remainingCount} ${plural(remainingCount, { one: 'risk', other: 'risks' })} left for assessment`}
                        />
                    </Box>

                    <Box>
                        <Card
                            title={t`Risks posture`}
                            body={
                                <DataPosture
                                    size="lg"
                                    boxes={riskPostureBoxes}
                                />
                            }
                        />
                    </Box>
                </Grid>
            )}

            <AppDatatable
                isLoading={isLoading}
                tableId="vendor-risks-datatable"
                total={risksQuery.data?.total ?? 0}
                data-id="vendor-risks-overview-datatable"
                columns={getTableColumns()}
                data={risks}
                defaultPaginationOptions={DEFAULT_PAGINATION_OPTIONS}
                filterProps={getVendorRisksFilters()}
                tableActions={getTableActions({
                    onDownload: downloadVendorRisksReport,
                    isDownloadLoading,
                })}
                filterViewModeProps={{
                    viewMode: 'toggleable',
                }}
                tableSearchProps={{
                    placeholder: t`Search`,
                    hideSearch: false,
                    debounceDelay: 1000,
                    defaultValue: '',
                }}
                emptyStateProps={
                    hasFilter
                        ? {
                              title: t`No risks match your search or filters`,
                              description: t`Try adjusting your search terms or filters to find what you're looking for.`,
                              illustrationName: 'Search',
                          }
                        : undefined
                }
                onFetchData={loadRisks}
                onRowClick={({ row }) => {
                    handleOpenDetailsPanel(row.riskId, () => (
                        <VendorsRisksOverviewPanelComponent data-id="o6TMqNJn" />
                    ));
                }}
            />
        </>
    );
});
