import { describe, expect, test } from 'vitest';
import { getPostureValueFromThresholdId } from './get-posture-value-from-threshold-id.helper';

describe('getPostureValueFromThresholdId', () => {
    const mockThresholds = [
        { id: 1, name: 'Low' },
        { id: 2, name: 'Medium' },
        { id: 3, name: 'High' },
        { id: 4, name: 'Critical' },
    ];

    describe('basic functionality', () => {
        test('returns "LOW" for threshold with "Low" name', () => {
            const result = getPostureValueFromThresholdId('1', mockThresholds);

            expect(result).toBe('LOW');
        });

        test('returns "MEDIUM" for threshold with "Medium" name', () => {
            const result = getPostureValueFromThresholdId('2', mockThresholds);

            expect(result).toBe('MEDIUM');
        });

        test('returns "HIGH" for threshold with "High" name', () => {
            const result = getPostureValueFromThresholdId('3', mockThresholds);

            expect(result).toBe('HIGH');
        });

        test('returns "CRITICAL" for threshold with "Critical" name', () => {
            const result = getPostureValueFromThresholdId('4', mockThresholds);

            expect(result).toBe('CRITICAL');
        });
    });

    describe('case insensitive matching', () => {
        const mixedCaseThresholds = [
            { id: 1, name: 'LOW' },
            { id: 2, name: 'MEDIUM' },
            { id: 3, name: 'HIGH' },
            { id: 4, name: 'CRITICAL' },
        ];

        test('handles uppercase threshold names', () => {
            expect(
                getPostureValueFromThresholdId('1', mixedCaseThresholds),
            ).toBe('LOW');
            expect(
                getPostureValueFromThresholdId('2', mixedCaseThresholds),
            ).toBe('MEDIUM');
            expect(
                getPostureValueFromThresholdId('3', mixedCaseThresholds),
            ).toBe('HIGH');
            expect(
                getPostureValueFromThresholdId('4', mixedCaseThresholds),
            ).toBe('CRITICAL');
        });

        test('handles mixed case threshold names', () => {
            const mixedThresholds = [
                { id: 1, name: 'LoW' },
                { id: 2, name: 'MeDiUm' },
                { id: 3, name: 'HiGh' },
                { id: 4, name: 'CrItIcAl' },
            ];

            expect(getPostureValueFromThresholdId('1', mixedThresholds)).toBe(
                'LOW',
            );
            expect(getPostureValueFromThresholdId('2', mixedThresholds)).toBe(
                'MEDIUM',
            );
            expect(getPostureValueFromThresholdId('3', mixedThresholds)).toBe(
                'HIGH',
            );
            expect(getPostureValueFromThresholdId('4', mixedThresholds)).toBe(
                'CRITICAL',
            );
        });
    });

    describe('partial name matching', () => {
        test('matches names containing keywords', () => {
            const descriptiveThresholds = [
                { id: 1, name: 'Low Risk Level' },
                { id: 2, name: 'Medium Risk Assessment' },
                { id: 3, name: 'High Priority Risk' },
                { id: 4, name: 'Critical Security Risk' },
            ];

            expect(
                getPostureValueFromThresholdId('1', descriptiveThresholds),
            ).toBe('LOW');
            expect(
                getPostureValueFromThresholdId('2', descriptiveThresholds),
            ).toBe('MEDIUM');
            expect(
                getPostureValueFromThresholdId('3', descriptiveThresholds),
            ).toBe('HIGH');
            expect(
                getPostureValueFromThresholdId('4', descriptiveThresholds),
            ).toBe('CRITICAL');
        });

        test('handles "moderate" as synonym for "medium"', () => {
            const moderateThresholds = [
                { id: 1, name: 'Low' },
                { id: 2, name: 'Moderate' },
                { id: 3, name: 'High' },
            ];

            expect(
                getPostureValueFromThresholdId('2', moderateThresholds),
            ).toBe('MEDIUM');
        });

        test('handles "moderate" in descriptive names', () => {
            const moderateThresholds = [
                { id: 1, name: 'Low Risk' },
                { id: 2, name: 'Moderate Risk Level' },
                { id: 3, name: 'High Risk' },
            ];

            expect(
                getPostureValueFromThresholdId('2', moderateThresholds),
            ).toBe('MEDIUM');
        });
    });

    describe('edge cases', () => {
        test('returns "LOW" when threshold ID is not found', () => {
            const result = getPostureValueFromThresholdId(
                '999',
                mockThresholds,
            );

            expect(result).toBe('LOW');
        });

        test('returns "LOW" when thresholds array is empty', () => {
            const result = getPostureValueFromThresholdId('1', []);

            expect(result).toBe('LOW');
        });

        test('handles numeric threshold ID as string', () => {
            const result = getPostureValueFromThresholdId('3', mockThresholds);

            expect(result).toBe('HIGH');
        });

        test('handles invalid threshold ID format', () => {
            const result = getPostureValueFromThresholdId(
                'invalid',
                mockThresholds,
            );

            expect(result).toBe('LOW');
        });

        test('returns "LOW" for threshold names that do not match any keywords', () => {
            const unknownThresholds = [
                { id: 1, name: 'Unknown Risk Type' },
                { id: 2, name: 'Custom Level' },
            ];

            expect(getPostureValueFromThresholdId('1', unknownThresholds)).toBe(
                'LOW',
            );
            expect(getPostureValueFromThresholdId('2', unknownThresholds)).toBe(
                'LOW',
            );
        });
    });

    describe('priority order', () => {
        test('prioritizes "critical" over other keywords in same name', () => {
            const conflictingThresholds = [
                { id: 1, name: 'High Critical Risk' },
            ];

            expect(
                getPostureValueFromThresholdId('1', conflictingThresholds),
            ).toBe('CRITICAL');
        });

        test('prioritizes "high" over "medium" in same name', () => {
            const conflictingThresholds = [{ id: 1, name: 'Medium High Risk' }];

            expect(
                getPostureValueFromThresholdId('1', conflictingThresholds),
            ).toBe('HIGH');
        });

        test('prioritizes "MEDIUM" over "LOW" in same name', () => {
            const conflictingThresholds = [{ id: 1, name: 'Low Medium Risk' }];

            expect(
                getPostureValueFromThresholdId('1', conflictingThresholds),
            ).toBe('MEDIUM');
        });
    });

    describe('real-world scenarios', () => {
        test('handles typical risk threshold configurations', () => {
            const realWorldThresholds = [
                { id: 1, name: 'Low Risk (0-3)' },
                { id: 2, name: 'Moderate Risk (4-6)' },
                { id: 3, name: 'High Risk (7-8)' },
                { id: 4, name: 'Critical Risk (9-10)' },
            ];

            expect(
                getPostureValueFromThresholdId('1', realWorldThresholds),
            ).toBe('LOW');
            expect(
                getPostureValueFromThresholdId('2', realWorldThresholds),
            ).toBe('MEDIUM');
            expect(
                getPostureValueFromThresholdId('3', realWorldThresholds),
            ).toBe('HIGH');
            expect(
                getPostureValueFromThresholdId('4', realWorldThresholds),
            ).toBe('CRITICAL');
        });

        test('handles alternative naming conventions', () => {
            const alternativeThresholds = [
                { id: 1, name: 'Green' },
                { id: 2, name: 'Yellow - Medium Risk' },
                { id: 3, name: 'Orange - High Risk' },
                { id: 4, name: 'Red - Critical Risk' },
            ];

            expect(
                getPostureValueFromThresholdId('1', alternativeThresholds),
            ).toBe('LOW');
            expect(
                getPostureValueFromThresholdId('2', alternativeThresholds),
            ).toBe('MEDIUM');
            expect(
                getPostureValueFromThresholdId('3', alternativeThresholds),
            ).toBe('HIGH');
            expect(
                getPostureValueFromThresholdId('4', alternativeThresholds),
            ).toBe('CRITICAL');
        });
    });
});
