/**
 * Maps threshold ID to risk posture filter value.
 *
 * @param thresholdId - The threshold ID as a string.
 * @param thresholds - Array of threshold objects with id and name properties.
 * @returns The corresponding risk posture filter value ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL').
 */
export const getPostureValueFromThresholdId = (
    thresholdId: string,
    thresholds: { id: number; name: string }[],
): string => {
    const threshold = thresholds.find(
        (item) => item.id === Number(thresholdId),
    );

    if (!threshold) {
        return 'LOW';
    }

    // Map threshold names to filter values
    const name = threshold.name.toLowerCase();

    if (name.includes('critical')) {
        return 'CRITICAL';
    }
    if (name.includes('high')) {
        return 'HIGH';
    }
    if (name.includes('medium') || name.includes('moderate')) {
        return 'MEDIUM';
    }

    return 'LOW';
};
