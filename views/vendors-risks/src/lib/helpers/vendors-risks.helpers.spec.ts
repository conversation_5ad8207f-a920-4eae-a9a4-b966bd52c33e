import { describe, expect, test } from 'vitest';
import type { Filter } from '@cosmos/components/filter-field';
import { getVendorRisksFilters } from './vendors-risks.helpers';

/**
 * Helper function to type-cast radio filters.
 */
const getRadioFilter = (filter: Filter | undefined) => {
    if (!filter || filter.filterType !== 'radio') {
        return undefined;
    }

    return filter as Filter & {
        filterType: 'radio';
        options: { label: string; value: string }[];
    };
};

describe('vendors-risks.helpers', () => {
    describe('getVendorRisksFilters', () => {
        test('returns correct filter structure', () => {
            const filters = getVendorRisksFilters();

            expect(filters).toHaveProperty('clearAllButtonLabel');
            expect(filters).toHaveProperty('filters');
            expect(filters).toHaveProperty('triggerLabel');
            expect(Array.isArray(filters.filters)).toBeTruthy();
            expect(filters.filters).toHaveLength(2);
        });

        test('includes risk posture filter with correct options', () => {
            const filters = getVendorRisksFilters();
            const riskPostureFilter = getRadioFilter(
                filters.filters.find((filter) => filter.id === 'risk-posture'),
            );

            expect(riskPostureFilter).toBeDefined();
            expect(riskPostureFilter?.filterType).toBe('radio');
            expect(riskPostureFilter?.label).toBe('Risk posture');
            expect(riskPostureFilter?.options).toHaveLength(4);

            const optionValues = riskPostureFilter?.options.map(
                (option) => option.value,
            );

            expect(optionValues).toStrictEqual([
                'LOW',
                'MEDIUM',
                'HIGH',
                'CRITICAL',
            ]);
        });

        test('includes status filter with correct options', () => {
            const filters = getVendorRisksFilters();
            const statusFilter = getRadioFilter(
                filters.filters.find((filter) => filter.id === 'status'),
            );

            expect(statusFilter).toBeDefined();
            expect(statusFilter?.filterType).toBe('radio');
            expect(statusFilter?.label).toBe('Risk status');
            expect(statusFilter?.options).toHaveLength(3);

            const optionValues = statusFilter?.options.map(
                (option) => option.value,
            );

            expect(optionValues).toStrictEqual([
                'ACTIVE',
                'CLOSED',
                'ARCHIVED',
            ]);
        });

        test('status filter options have correct labels', () => {
            const filters = getVendorRisksFilters();
            const statusFilter = getRadioFilter(
                filters.filters.find((filter) => filter.id === 'status'),
            );

            const options = statusFilter?.options;

            expect(options).toBeDefined();

            const activeOption = options?.find(
                (option) => option.value === 'ACTIVE',
            );

            expect(activeOption?.label).toBe('Active');

            const closedOption = options?.find(
                (option) => option.value === 'CLOSED',
            );

            expect(closedOption?.label).toBe('Closed');

            const archivedOption = options?.find(
                (option) => option.value === 'ARCHIVED',
            );

            expect(archivedOption?.label).toBe('Archived');
        });

        test('risk posture filter options have correct labels', () => {
            const filters = getVendorRisksFilters();
            const riskPostureFilter = getRadioFilter(
                filters.filters.find((filter) => filter.id === 'risk-posture'),
            );

            const options = riskPostureFilter?.options;

            expect(options).toBeDefined();

            const lowOption = options?.find((option) => option.value === 'LOW');

            expect(lowOption?.label).toBe('Low');

            const mediumOption = options?.find(
                (option) => option.value === 'MEDIUM',
            );

            expect(mediumOption?.label).toBe('Medium');

            const highOption = options?.find(
                (option) => option.value === 'HIGH',
            );

            expect(highOption?.label).toBe('High');

            const criticalOption = options?.find(
                (option) => option.value === 'CRITICAL',
            );

            expect(criticalOption?.label).toBe('Critical');
        });

        test('has correct clear all button label', () => {
            const filters = getVendorRisksFilters();

            expect(filters.clearAllButtonLabel).toBe('Reset');
        });

        test('has correct trigger label', () => {
            const filters = getVendorRisksFilters();

            expect(filters.triggerLabel).toBe('Filters');
        });
    });
});
