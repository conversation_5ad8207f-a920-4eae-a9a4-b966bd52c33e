import { AppDatatable } from '@components/app-datatable';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { observer } from '@globals/mobx';
import { RISK_REGISTER_MITIGATING_CONTROLS_COLUMNS } from '../constants/columns.constant';

export const RiskMitigationControlsTableComponent = observer(
    (): React.JSX.Element => {
        const { isLoading, paginatedControls, loadControls } =
            sharedRiskDetailsController;

        return (
            <AppDatatable
                // isRowSelectionEnabled
                isLoading={isLoading}
                tableId="risk-register-mitigating-controls-datatable"
                total={paginatedControls.total}
                data={paginatedControls.data}
                columns={RISK_REGISTER_MITIGATING_CONTROLS_COLUMNS}
                data-id="iyYzVX4r"
                emptyStateProps={{
                    title: 'No controls found',
                    description: 'No controls found',
                }}
                tableActions={[
                    {
                        actionType: 'button',
                        id: 'map-controls-button',
                        typeProps: {
                            label: 'Map controls',
                            level: 'secondary',
                        },
                    },
                ]}
                // TODO https://drata.atlassian.net/browse/ENG-66438
                filterProps={{
                    clearAllButtonLabel: 'Reset filters',
                    filters: [],
                    triggerLabel: 'Filter',
                }}
                onFetchData={loadControls}
            />
        );
    },
);
