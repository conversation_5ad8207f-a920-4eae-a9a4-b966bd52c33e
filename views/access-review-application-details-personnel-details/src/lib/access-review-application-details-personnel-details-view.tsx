import { getPersonnelStatusLabel } from '@components/access-review';
import {
    AccessReviewPersonnelDetailsCard,
    type AccessReviewPersonnelDetailsCardProps,
    applicationDetailsAdaptor,
    groupsToKVPValueAdaptor,
    hasMfaToKVPAdaptor,
} from '@components/access-review-details';
import { sharedAccessReviewPeriodApplicationUserController } from '@controllers/access-reviews';
import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import { Skeleton } from '@cosmos/components/skeleton';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { AccessReviewPeriodApplicationUserResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';

const accessReviewPeriodApplicationUserDetailsAdaptor = (
    data?: Partial<AccessReviewPeriodApplicationUserResponseDto> | null,
    logo?: string | null,
): AccessReviewPersonnelDetailsCardProps => {
    return {
        applicationDetails: applicationDetailsAdaptor(data, logo),
        accountName: data?.account || 'None',
        email: data?.user?.email || undefined,
        jobTitle: data?.user?.jobTitle || (
            <EmptyValue label="JobTitleEmptyValue" />
        ),
        employmentStatus: getPersonnelStatusLabel(data?.user?.employmentStatus),
        groups: groupsToKVPValueAdaptor(data?.groups),
        mfa: hasMfaToKVPAdaptor(
            data?.hasMFA,
        ) as AccessReviewPersonnelDetailsCardProps['mfa'],
        permissionLink: data?.accessData?.permissionsLinks?.[0] ?? null,
        rawAccessData: JSON.stringify(data?.accessData?.raw ?? {}, null, '\t'),
        connection: {
            clientAlias: data?.connection?.clientAlias ?? '',
            accountId: data?.connection?.accountId ?? '',
            clientType: data?.connection?.clientType ?? '',
            user: data?.connection?.user
                ? { roles: data.connection.user.roles ?? [] }
                : null,
        },
    };
};

export const AccessReviewApplicationDetailsPersonnelDetailsView = observer(
    (): React.JSX.Element => {
        const { isLoading, accessReviewPeriodApplicationUserDetails } =
            sharedAccessReviewPeriodApplicationUserController;

        const {
            isLoading: isLoadingProvider,
            accessReviewsApplicationsProvider,
        } = activeAccessReviewsApplicationsController;

        if (isLoading || isLoadingProvider) {
            return <Skeleton barCount={1} width="100%" />;
        }

        const {
            applicationDetails,
            accountName,
            email,
            jobTitle,
            employmentStatus,
            groups,
            mfa,
            permissionLink,
            rawAccessData,
            connection,
        } = accessReviewPeriodApplicationUserDetailsAdaptor(
            accessReviewPeriodApplicationUserDetails,
            accessReviewsApplicationsProvider.logo,
        );

        return (
            <AccessReviewPersonnelDetailsCard
                data-id="mE8rXYC-"
                applicationDetails={applicationDetails}
                accountName={accountName}
                email={email}
                jobTitle={jobTitle}
                employmentStatus={employmentStatus}
                groups={groups}
                mfa={mfa}
                permissionLink={permissionLink}
                rawAccessData={rawAccessData}
                connection={connection}
            />
        );
    },
);
