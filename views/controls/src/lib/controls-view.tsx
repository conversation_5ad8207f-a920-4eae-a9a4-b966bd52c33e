import { isString } from 'lodash-es';
import { useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedControlsController } from '@controllers/controls';
import {
    type DatatableRef,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useQueryParams } from '@globals/use-query-params';
import { useNavigate } from '@remix-run/react';
import { getControlsListColumns } from '../helpers/get-control-list-columns.helper';
import { ControlListFilterModel } from '../models/control-list-filter.model';
import { ControlsBulkActionsModel } from '../models/controls-bulk-actions.model';
import { ControlsListTableActionsModel } from '../models/controls-list-table-actions.model';

const DEFAULT_PAGE_SIZE = 20;

export const ControlsView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const { params: { frameworkSlug } = {} } = useQueryParams();

    const datatableRef = useRef<DatatableRef>(null);
    const { controls, total, isLoading, loadPage, currentAppliedFilters } =
        sharedControlsController;
    const { filters } = new ControlListFilterModel(
        isString(frameworkSlug) ? frameworkSlug : undefined,
    );
    const { tableActions } = new ControlsListTableActionsModel();
    const { bulkActions, handleRowSelection } = new ControlsBulkActionsModel(
        currentAppliedFilters,
        datatableRef,
    );

    const { hasWriteControlPermission } = sharedFeatureAccessModel;

    return (
        <AppDatatable
            isFullPageTable
            getRowId={(row) => String(row.id)}
            isRowSelectionEnabled={hasWriteControlPermission}
            imperativeHandleRef={datatableRef}
            isLoading={isLoading}
            tableId="datatable-controls-list"
            total={total}
            data={controls}
            columns={getControlsListColumns()}
            filterProps={filters}
            data-testid="ControlsView"
            data-id="48FDfCWc"
            tableActions={tableActions}
            bulkActionDropdownItems={bulkActions}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: t`Controls`,
                description: t`No controls were found`,
            }}
            tableSearchProps={{
                placeholder: t`Search controls by name, description, code or requirements...`,
            }}
            defaultPaginationOptions={{
                pageIndex: 0,
                pageSize: DEFAULT_PAGE_SIZE,
                pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
            }}
            onRowSelection={handleRowSelection}
            onFetchData={loadPage}
            onRowClick={({ row }) => {
                navigate(`${row.id}/overview`);
            }}
        />
    );
});
