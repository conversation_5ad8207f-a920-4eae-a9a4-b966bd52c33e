import {
    sharedMonitoringCodeController,
    sharedMonitoringStatsController,
} from '@controllers/monitoring';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { MonitoringCodeBulkActionsModel } from './models/monitoring-code-bulk-actions.model';
import {
    MONITORING_CODE_COLUMNS,
    MONITORING_CODE_FILTERS,
} from './monitoring-code.constants';

export const MonitoringCodeView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const { bulkActions, handleRowSelection } =
        new MonitoringCodeBulkActionsModel();

    const {
        monitoringCodeListData,
        monitoringCodeTotal,
        monitoringCodeListLoad,
        isLoading,
    } = sharedMonitoringCodeController;
    const {
        monitoringTotalRepositories,
        monitoringNumRepositoriesMonitored,
        monitoringStatsFailedTests,
        monitoringStatsPassedTests,
        isLoadingStats,
    } = sharedMonitoringStatsController;

    return (
        <>
            {isLoadingStats ? (
                <Grid pb="4x" pt="8x" columns="1">
                    <Card
                        title={t`Tests`}
                        body={<Loader isSpinnerOnly label={t`Loading...`} />}
                    />
                </Grid>
            ) : (
                <Grid columns="3" gap="4x" pb="4x" pt="8x">
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Repositories monitored`}
                            statValue={
                                monitoringTotalRepositories /
                                monitoringNumRepositoriesMonitored
                            }
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Failed tests`}
                            statValue={monitoringStatsFailedTests}
                            statIcon="Cancel"
                            statIconColor="critical"
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Passed tests`}
                            statValue={monitoringStatsPassedTests}
                            statIcon="CheckCircle"
                            statIconColor="success"
                        />
                    </Box>
                </Grid>
            )}

            <Datatable
                isRowSelectionEnabled
                isLoading={isLoading}
                data={monitoringCodeListData}
                columns={MONITORING_CODE_COLUMNS}
                total={monitoringCodeTotal}
                filterProps={MONITORING_CODE_FILTERS}
                tableId="datatable-monitoring-code"
                data-id="datatable-monitoring-code"
                data-testid="MonitoringCodeView"
                bulkActionDropdownItems={bulkActions}
                getRowId={(row) => String(row.testId)}
                emptyStateProps={{
                    title: t`No monitoring code found`,
                    description: t`No monitoring code found`,
                }}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t`Search`,
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={monitoringCodeListLoad}
                onRowSelection={handleRowSelection}
                onRowClick={({ row }) => {
                    navigate(
                        `/compliance/monitoring/code/details/${row.testId}`,
                    );
                }}
            />
        </>
    );
});
