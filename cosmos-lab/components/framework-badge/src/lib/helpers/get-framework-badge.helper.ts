import type { TagType } from '../types';
import type { FrameworkBadgeName } from '../types/framework-badge-name.type';

export const getFrameworkBadge = (tagType: TagType): FrameworkBadgeName => {
    // eslint-disable-next-line sonarjs/max-switch-cases -- This function needs to handle all framework types (30+ cases) to map API framework tags to display badge names. Breaking this into smaller functions would reduce readability and performance for this mapping logic.
    switch (tagType) {
        case 'CIS8': {
            return 'CIS8';
        }
        case 'CCM': {
            return 'CCM';
        }
        case 'CCPA': {
            return 'CCPA';
        }
        case 'CMMC': {
            return 'CMMC';
        }
        case 'COBIT': {
            return 'COBIT';
        }
        case 'CPRA': {
            return 'CPRA';
        }
        case 'CYBER_ESSENTIALS': {
            return 'CYBER_ESSENTIALS';
        }
        case 'CYBER_ESSENTIALS_32': {
            return 'CYBER_ESSENTIALS_32';
        }
        case 'DORA': {
            return 'DORA';
        }
        case 'DPF': {
            return 'DPF';
        }
        case 'DRATA_ESSENTIALS': {
            return 'DRATA_ESSENTIALS';
        }
        case 'FED_RAMP':
        case 'FEDRAMP': {
            return 'FED_RAMP';
        }
        case 'FFIEC': {
            return 'FFIEC';
        }
        case 'GDPR': {
            return 'GDPR';
        }
        case 'HIPAA': {
            return 'HIPAA';
        }
        case 'HITRUST': {
            return 'HITRUST';
        }
        case 'ISO_27001':
        case 'ISO27001':
        case 'ISO270012022':
        case 'ISO_27001_2022': {
            return 'ISO_27001';
        }
        case 'ISO270172015':
        case 'ISO_27017':
        case 'ISO_27017_2015': {
            return 'ISO_27017';
        }
        case 'ISO270182019':
        case 'ISO_27018':
        case 'ISO_27018_2019': {
            return 'ISO_27018';
        }
        case 'ISO27701':
        case 'ISO_27701': {
            return 'ISO_27701';
        }
        case 'ISO420012023':
        case 'ISO_42001':
        case 'ISO_42001_2023': {
            return 'ISO_42001';
        }
        case 'MICROSOFT_SSPA':
        case 'MSSSPA': {
            return 'MICROSOFT_SSPA';
        }
        case 'NIS_2':
        case 'NIS2': {
            return 'NIS_2';
        }
        case 'NIST_800_53':
        case 'NIST80053': {
            return 'NIST_800_53';
        }
        case 'NIST800171':
        case 'NIST_800_171':
        case 'NIST800171R3': {
            return 'NIST_800_171';
        }
        case 'NIST_AI_RMF':
        case 'NISTAI': {
            return 'NIST_AI_RMF';
        }
        case 'NIST_CSF':
        case 'NISTCSF': {
            return 'NIST_CSF';
        }
        case 'NIST_CSF_20':
        case 'NISTCSF2': {
            return 'NIST_CSF_20';
        }
        case 'PCI':
        case 'PCI_DSS':
        case 'PCI4': {
            return 'PCI_DSS';
        }
        case 'SOC_2':
        case 'SOC_2_TYPE_1':
        case 'SOC_2_TYPE_2': {
            return 'SOC_2';
        }
        case 'SOX_ITGC': {
            return 'SOX_ITGC';
        }
        case 'NONE':
        case 'SCF':
        case 'CUSTOM':
        default: {
            return 'CUSTOM';
        }
    }
};
