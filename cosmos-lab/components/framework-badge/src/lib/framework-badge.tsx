import { styled } from 'styled-components';
import { illustrationNeutralNone } from '@cosmos/constants/tokens';
import { COLOR_SCHEMES } from './constants/color-scheme.constant';
import { FRAMEWORK_BADGES } from './constants/framework-badges.constant';
import { SIZES } from './constants/sizes.constant';
import { getFrameworkBadge } from './helpers/get-framework-badge.helper';
import type { FrameworkBadgeProps } from './types';
import type { ColorScheme } from './types/color-scheme.type';

const StyledFrameworkBadgeDiv = styled.div<{
    $colorScheme: ColorScheme;
    $size: string;
}>`
    height: ${({ $size }) => $size};
    width: ${({ $size }) => $size};
    min-width: ${({ $size }) => $size};

    // This is needed for custom icons to use currentColor
    ${({ $colorScheme: { color } }) => {
        return `
            color: ${color};
        `;
    }}

    // ensure svg fills size of wrapper component
    svg {
        height: 100%;
        width: 100%;
    }
`;

const StyledFrameworkBadgeCircle = styled.circle.attrs({
    fill: illustrationNeutralNone,
})``;

const StyledFrameworkBadgeLogoG = styled.g.attrs({
    fill: illustrationNeutralNone,
})``;

/**
 * [AI generated] Displays framework identification badges with logos and names for compliance standards like SOC 2, ISO 27001, and GDPR with consistent styling.
 *
 * 🚧 Needs Figma Link.
 */
export const FrameworkBadge = ({
    badgeName,
    size = 'md',
    'data-id': dataId = 'framework-badge',
    colorScheme = 'primary',
}: FrameworkBadgeProps): React.JSX.Element => {
    const frameworkBadgeName = getFrameworkBadge(badgeName);
    const BadgeToDisplay = FRAMEWORK_BADGES[frameworkBadgeName];
    const COLOR_SCHEME = COLOR_SCHEMES[colorScheme];

    const { frameworkBadgeSize } = SIZES[size];

    return (
        <StyledFrameworkBadgeDiv
            $size={frameworkBadgeSize}
            data-id={`${dataId}-${badgeName}`}
            $colorScheme={COLOR_SCHEME}
            data-testid="FrameworkBadge"
        >
            <svg
                width="64"
                height="64"
                viewBox="0 0 64 64"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                data-testid="FrameworkBadgeSVG"
                data-id="_5p-0urA"
            >
                <linearGradient
                    id="base-gradient-_5p-0urA"
                    x1={50.8}
                    y1={56.642}
                    x2={11.02}
                    y2={0.413}
                    gradientUnits="userSpaceOnUse"
                >
                    <stop offset={0.12} stopColor="#fff" stopOpacity={0.2} />
                    <stop offset={0.42} stopColor="#fff" />
                </linearGradient>
                <mask
                    id="base-mask-_5p-0urA"
                    width={58}
                    height={58}
                    x={3}
                    y={3}
                    maskUnits="userSpaceOnUse"
                >
                    <path
                        fill="url(#base-gradient-_5p-0urA)"
                        fillRule="evenodd"
                        d="M32 60c15.464 0 28-12.536 28-28S47.464 4 32 4 4 16.536 4 32s12.536 28 28 28m0 1c16.016 0 29-12.984 29-29S48.016 3 32 3 3 15.984 3 32s12.984 29 29 29"
                        clipRule="evenodd"
                    />
                </mask>
                <circle fill="currentColor" cx={32} cy={32} r={32} />
                <StyledFrameworkBadgeLogoG>
                    <BadgeToDisplay />
                </StyledFrameworkBadgeLogoG>
                <StyledFrameworkBadgeCircle
                    mask="url(#base-mask-_5p-0urA)"
                    cx={32}
                    cy={32}
                    r={30}
                />
            </svg>
        </StyledFrameworkBadgeDiv>
    );
};
