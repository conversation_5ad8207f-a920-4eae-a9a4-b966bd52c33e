import type {
    AuditorFrameworkResponseDto,
    FrameworkResponseDto,
    FrameworkTagEnum,
} from '@globals/api-sdk/types';
import type { FrameworkBadgeName } from './framework-badge-name.type';

export type TagType =
    | FrameworkResponseDto['tag']
    | AuditorFrameworkResponseDto['type']
    | FrameworkTagEnum
    // Custom framework types that aren't included in the API response DTOs
    | 'CUSTOM'
    | 'DPF'
    | 'HITRUST'
    // Framework badge names for display purposes (mapped from API values)
    | FrameworkBadgeName;
