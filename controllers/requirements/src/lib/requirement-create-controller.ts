import { isNil, isString } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    customFrameworksControllerGetCustomCategoriesOptions,
    customFrameworksControllerSaveCustomRequirementMutation,
    customFrameworksControllerValidateCustomRequirementCodeOptions,
} from '@globals/api-sdk/queries';
import type { GetCustomCategoryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { FormValues } from '@ui/forms';

class RequirementCreateController {
    #getRequirementCategories = new ObservedQuery(
        customFrameworksControllerGetCustomCategoriesOptions,
    );

    #saveRequirementMutation = new ObservedMutation(
        customFrameworksControllerSaveCustomRequirementMutation,
    );

    #validateRequirementCode = new ObservedQuery(
        customFrameworksControllerValidateCustomRequirementCodeOptions,
    );

    #frameworkId: number | null = null;

    #isCreateComplete = false;

    constructor() {
        makeAutoObservable(this);
    }

    setFrameworkId(frameworkId: number): void {
        this.#frameworkId = frameworkId;
    }

    loadRequirementCategories(frameworkId: number): void {
        this.#getRequirementCategories.load({
            path: { frameworkId },
            query: {
                page: 1,
            },
        });
    }

    get requirementCategories(): GetCustomCategoryResponseDto[] {
        return this.#getRequirementCategories.data?.data ?? [];
    }

    get isRequirementCategoriesLoading(): boolean {
        return this.#getRequirementCategories.isLoading;
    }

    get isCreateComplete(): boolean {
        return this.#isCreateComplete;
    }

    saveRequirement = (
        formValues: FormValues,
        onSuccess?: () => void,
    ): void => {
        this.#isCreateComplete = false;

        if (isNil(this.#frameworkId)) {
            console.error('Framework ID is not set');

            return;
        }

        if (isNil(sharedWorkspacesController.currentWorkspace)) {
            console.error('Workspace ID is not set');

            return;
        }

        this.#validateRequirementCode.load({
            path: { customFrameworkId: this.#frameworkId },
            query: { requirementCode: formValues.requirementCode as string },
        });

        when(
            () => !this.#validateRequirementCode.isLoading,
            () => {
                if (this.#validateRequirementCode.hasError) {
                    console.error(
                        'Full error object:',
                        this.#validateRequirementCode.data,
                    );

                    snackbarController.addSnackbar({
                        id: 'requirement-save-error',
                        props: {
                            title: t`Unable to save requirement`,
                            description: t`An error occurred while saving the requirement. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    if (isNil(this.#frameworkId)) {
                        console.error('Framework ID is not set');
                        snackbarController.addSnackbar({
                            id: 'requirement-save-error',
                            props: {
                                title: t`Unable to save requirement`,
                                description: t`An error occurred while saving the requirement. Please try again.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return;
                    }

                    const requirement =
                        this.#validateRequirementCode.data?.requirement;

                    if (
                        !isNil(requirement) &&
                        isString(requirement.code) &&
                        isString(formValues.requirementCode) &&
                        requirement.code.toLocaleLowerCase().trim() ===
                            formValues.requirementCode
                                .toLocaleLowerCase()
                                .trim()
                    ) {
                        snackbarController.addSnackbar({
                            id: 'requirement-code-validation-error',
                            props: {
                                title: t`Unable to save requirement`,
                                description: t`The requirement code is already in use. Please choose another code.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return;
                    }

                    const requirementCategory =
                        formValues.requirementCategory as
                            | {
                                  id: string;
                                  label: string;
                                  value: string;
                              }
                            | undefined;

                    this.#saveRequirementMutation.mutate({
                        body: {
                            code: (formValues.requirementCode ?? '') as string,
                            name: (formValues.requirementName ?? '') as string,
                            category: requirementCategory?.value,
                            description: formValues.description as string,
                            additionalInfo: formValues.additionalInfo as string,
                            frameworkId: this.#frameworkId,
                            workspaceId:
                                sharedWorkspacesController.currentWorkspace?.id,
                        },
                    });

                    when(
                        () => !this.#saveRequirementMutation.isPending,
                        () => {
                            if (this.#saveRequirementMutation.hasError) {
                                console.error(
                                    'Full error object:',
                                    this.#saveRequirementMutation.error,
                                );
                                console.error(
                                    'Error structure:',
                                    JSON.stringify(
                                        this.#saveRequirementMutation.error,
                                        null,
                                        2,
                                    ),
                                );

                                snackbarController.addSnackbar({
                                    id: 'requirement-save-error',
                                    props: {
                                        title: t`Unable to save requirement`,
                                        description: t`An error occurred while saving the requirement. Please try again.`,
                                        severity: 'critical',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });
                            } else {
                                snackbarController.addSnackbar({
                                    id: 'requirement-save-success',
                                    props: {
                                        title: t`Requirement saved`,
                                        description: t`The requirement was saved successfully.`,
                                        severity: 'success',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });

                                this.#isCreateComplete = true;

                                onSuccess?.();
                            }
                        },
                    );
                }
            },
        );
    };

    reset = (): void => {
        this.#isCreateComplete = false;
    };
}

export const sharedRequirementCreateController =
    new RequirementCreateController();
