import { routeController, type UtilitiesName } from '@controllers/route';
import { makeAutoObservable } from '@globals/mobx';
import { sharedCustomerRequestUtilitiesNotesController } from './customer-request-utilities-notes-controller';
import type { UtilitiesBase, UtilitiesBaseConfig } from './utilities-base';
import { sharedUtilitiesNotesController } from './utilities-notes-controller';
import { sharedUtilitiesObservationsController } from './utilities-observations-controller';
import { sharedUtilitiesTasksController } from './utilities-tasks-controller';
import { sharedUtilitiesTicketsController } from './utilities-tickets-controller';

export class UtilitiesController {
    constructor() {
        makeAutoObservable(this);
    }

    get utilitiesList(): UtilitiesName[] {
        return routeController.utilities?.utilitiesList ?? [];
    }

    get isOpen(): boolean {
        return this.utilitiesList.some(
            (utility) => this.getUtilityControllerByName(utility).isOpen,
        );
    }

    get enabledUtilities(): UtilitiesName[] {
        return this.utilitiesList.filter(
            (utilityName) =>
                this.getUtilityControllerByName(utilityName).isEnabled,
        );
    }

    getUtilityControllerByName = (
        name: UtilitiesName,
    ): UtilitiesBase<UtilitiesBaseConfig> => {
        switch (name) {
            case 'notes_for_events':
            case 'notes_for_access_review_active_period_user':
            case 'notes_for_access_review_active_application':
            case 'notes_for_completed_application_on_completed_review':
            case 'notes_for_controls': {
                return sharedUtilitiesNotesController;
            }
            case 'notes_for_customer_requests': {
                return sharedCustomerRequestUtilitiesNotesController;
            }
            case 'tasks': {
                return sharedUtilitiesTasksController;
            }
            case 'tickets_for_access_review_active_period_user':
            case 'tickets_for_controls': {
                return sharedUtilitiesTicketsController;
            }
            case 'observations': {
                return sharedUtilitiesObservationsController;
            }
            //
            default: {
                // Not sure how to handle this
                return sharedUtilitiesNotesController;
            }
        }
    };

    closeAllUtilities = (): void => {
        this.utilitiesList.forEach((utilityName) => {
            this.getUtilityControllerByName(utilityName).closeUtility();
        });
    };

    handleOpenCloseAllTabsClick = (): void => {
        if (this.isOpen) {
            this.closeAllUtilities();
        } else {
            this.getUtilityControllerByName(
                this.utilitiesList[0],
            ).openUtility();
        }
    };
}

export const sharedUtilitiesController = new UtilitiesController();
