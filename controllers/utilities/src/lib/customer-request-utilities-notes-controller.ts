import { isEmpty, isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    customerRequestControllerCreateClientCustomerRequestMessageMutation,
    customerRequestControllerDeleteCustomerRequestMessageMutation,
    customerRequestControllerGetCustomerRequestFileSignedUrlOptions,
    customerRequestControllerGetCustomerRequestMessagesOptions,
    customerRequestControllerUpdateCustomerRequestMessageMutation,
    customerRequestControllerUpdateCustomerRequestNotificationsMutation,
} from '@globals/api-sdk/queries';
import type {
    NoteResponseDto,
    UserCardResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { NoteUpdateDto } from '../../../../components/utilities/src/lib/notes/utilities-notes-create-dto-types';
import { UtilitiesBase, type UtilitiesBaseConfig } from './utilities-base';

interface Message {
    'files[]'?: (Blob | File)[];
    fileMetadata?: {
        originalFile?: string;
        name?: string;
        creationDate?: string;
    }[];
    comment?: string;
}

export interface RawCustomerRequestMessage {
    id?: number;
    message?: string;
    sentAt?: string;
    hasBeenRead?: boolean;
    authorId?: number;
    author?: string;
    avatar?: string;
    authorIsAuditor?: boolean;
    files?: {
        id?: number;
        name?: string;
        url?: string;
    }[];
}

export interface CustomerRequestUtilitiesNotesConfig
    extends UtilitiesBaseConfig {
    overrides?: {
        enabled?: boolean;
    };
}

export class CustomerRequestUtilitiesNotesController extends UtilitiesBase<CustomerRequestUtilitiesNotesConfig> {
    customerRequestMessagesQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestMessagesOptions,
    );

    createMessageMutation = new ObservedMutation(
        customerRequestControllerCreateClientCustomerRequestMessageMutation,
    );

    updateMessageMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestMessageMutation,
    );

    deleteMessageMutation = new ObservedMutation(
        customerRequestControllerDeleteCustomerRequestMessageMutation,
    );

    updateNotificationsMutation = new ObservedMutation(
        customerRequestControllerUpdateCustomerRequestNotificationsMutation,
    );

    downloadAttachmentQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestFileSignedUrlOptions,
    );

    requestId: number | null = null;
    clientId: string | null = null;

    constructor() {
        super();
        makeObservable(this);
    }

    get messages(): NoteResponseDto[] {
        const rawMessages =
            this.customerRequestMessagesQuery.data?.messages ?? [];

        return rawMessages.map((message: RawCustomerRequestMessage) => ({
            id: String(message.id),
            comment: message.message,
            createdAt: message.sentAt,
            updatedAt: message.sentAt,
            hasBeenRead: message.hasBeenRead ?? false,
            owner: {
                id: message.authorId ?? 0,
                firstName: message.author,
                avatarUrl: message.avatar,
                createdAt: message.sentAt,
                authorIsAuditor: message.authorIsAuditor ?? false,
            } as UserCardResponseDto & { authorIsAuditor: boolean },
            noteFiles: (message.files ?? []).map((file, index: number) => ({
                id: String(file.id ?? index),
                name: file.name ?? `file-${index}`,
                file: file.url,
                createdAt: message.sentAt,
                updatedAt: message.sentAt,
            })),
        })) as NoteResponseDto[];
    }

    get isLoading(): boolean {
        return this.customerRequestMessagesQuery.isLoading;
    }

    loadMessages = (requestId: number, clientId: string): void => {
        if (isNil(requestId) || isNil(clientId)) {
            throw new Error(t`Request ID and Client ID are required`);
        }

        this.requestId = requestId;
        this.clientId = clientId;

        this.customerRequestMessagesQuery.load({
            path: {
                customerRequestId: requestId,
            },
        });
    };

    createNote = (values: Message): void => {
        const { comment } = values;

        if (!this.requestId) {
            throw new Error(t`Request ID is required`);
        }

        if (!comment) {
            return;
        }

        const timestamp = new Date().toISOString();

        // Prepare the request body with attachments if they exist
        const body: {
            message?: string;
            'files[]'?: File[];
        } = {};

        if (comment.trim()) {
            body.message = comment;
        }

        if (values['files[]'] && !isEmpty(values['files[]'])) {
            body['files[]'] = values['files[]'] as File[];
        }

        this.createMessageMutation
            .mutateAsync({
                path: {
                    customerRequestId: this.requestId,
                },
                body,
            })
            .then(() => {
                this.customerRequestMessagesQuery.invalidate();
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-customer-request-message-success`,
                    props: {
                        title: t`Message sent`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-created-customer-request-message-error`,
                    props: {
                        title: t`Unable to send message`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    updateNote = (messageId: string, values: NoteUpdateDto): void => {
        const { comment, filesToDelete } = values;
        const timestamp = new Date().toISOString();

        this.updateMessageMutation
            .mutateAsync({
                path: {
                    messageId: Number(messageId),
                },
                body: {
                    ...(comment.trim() && { message: comment }),
                    filesToDelete: filesToDelete?.map(Number),
                    'files[]': values['files[]'] as File[],
                },
            })
            .then(() => {
                this.customerRequestMessagesQuery.invalidate();
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-customer-request-message-success`,
                    props: {
                        title: t`Message updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-customer-request-message-error`,
                    props: {
                        title: t`Unable to update message`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    updateMessageReadStatus = (
        messageId: string,
        hasBeenRead: boolean,
    ): void => {
        if (!this.requestId) {
            throw new Error(t`Request ID is required`);
        }

        const timestamp = new Date().toISOString();

        this.updateNotificationsMutation
            .mutateAsync({
                path: {
                    customerRequestId: this.requestId,
                    messageId: Number(messageId),
                },
            })
            .then(() => {
                this.customerRequestMessagesQuery.invalidate();
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-message-read-status-success`,
                    props: {
                        title: hasBeenRead
                            ? t`Message marked as read`
                            : t`Message marked as unread`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `${timestamp}-updated-message-read-status-error`,
                    props: {
                        title: t`Unable to update message status`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    deleteNote = (messageId: string): void => {
        const timestamp = new Date().toISOString();

        openConfirmationModal({
            title: t`Delete message?`,
            body: t`If you delete this message? This action cannot be undone.`,
            confirmText: t`Yes, delete message`,
            cancelText: t`No, go back`,
            type: 'danger',
            onConfirm: () => {
                this.deleteMessageMutation
                    .mutateAsync({
                        path: {
                            messageId: Number(messageId),
                        },
                    })
                    .then(() => {
                        this.customerRequestMessagesQuery.invalidate();
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-customer-request-message-success`,
                            props: {
                                title: t`Message deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-customer-request-message-error`,
                            props: {
                                title: t`Unable to delete message`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    downloadNoteAttachment = (noteFileId: string): void => {
        this.downloadAttachmentQuery.load({
            path: { fileId: Number(noteFileId) },
        });
        when(() => !this.downloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.downloadAttachmentQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedCustomerRequestUtilitiesNotesController =
    new CustomerRequestUtilitiesNotesController();
