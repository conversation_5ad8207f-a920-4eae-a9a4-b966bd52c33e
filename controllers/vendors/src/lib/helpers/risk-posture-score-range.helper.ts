import { isEmpty, isNil } from 'lodash-es';
import type { RiskThresholdResponseDto } from '@globals/api-sdk/types';

/**
 * Maps risk posture filter values to score ranges based on thresholds.
 *
 * @param riskPosture - The risk posture value (low, moderate, medium, high, critical).
 * @param thresholds - Array of risk thresholds from settings.
 * @returns Object containing minScore and maxScore for the given risk posture.
 */
export const getRiskPostureScoreRange = (
    riskPosture: string,
    thresholds: RiskThresholdResponseDto[],
): { minScore?: number; maxScore?: number } => {
    if (!riskPosture || isEmpty(thresholds)) {
        return {};
    }

    // Sort thresholds by minThreshold to ensure correct mapping
    const sortedThresholds = [...thresholds].sort(
        (a, b) => a.minThreshold - b.minThreshold,
    );

    // Map risk posture values to threshold indices based on common patterns
    const postureToIndexMap: Record<string, number> = {
        low: 0,
        moderate: Math.min(1, sortedThresholds.length - 1),
        medium: Math.min(1, sortedThresholds.length - 1), // Support both 'moderate' and 'medium'
        high: Math.min(2, sortedThresholds.length - 1),
        critical: sortedThresholds.length - 1,
    };

    const thresholdIndex = postureToIndexMap[riskPosture.toLowerCase()];

    if (isNil(thresholdIndex) || thresholdIndex >= sortedThresholds.length) {
        return {};
    }

    const threshold = sortedThresholds[thresholdIndex];

    return {
        minScore: threshold.minThreshold,
        maxScore: threshold.maxThreshold,
    };
};
