import { describe, expect, test } from 'vitest';
import type { RiskThresholdResponseDto } from '@globals/api-sdk/types';
import { getRiskPostureScoreRange } from './risk-posture-score-range.helper';

describe('getRiskPostureScoreRange', () => {
    const mockThresholds: RiskThresholdResponseDto[] = [
        {
            id: 1,
            minThreshold: 0,
            maxThreshold: 3,
            color: 'green500',
            name: 'Low',
            description: 'Low risk threshold',
        },
        {
            id: 2,
            minThreshold: 4,
            maxThreshold: 6,
            color: 'yellow500',
            name: 'Moderate',
            description: 'Moderate risk threshold',
        },
        {
            id: 3,
            minThreshold: 7,
            maxThreshold: 10,
            color: 'red500',
            name: 'High',
            description: 'High risk threshold',
        },
    ];

    describe('basic functionality', () => {
        test('returns correct range for low risk posture', () => {
            const result = getRiskPostureScoreRange('low', mockThresholds);

            expect(result).toStrictEqual({ minScore: 0, maxScore: 3 });
        });

        test('returns correct range for moderate risk posture', () => {
            const result = getRiskPostureScoreRange('moderate', mockThresholds);

            expect(result).toStrictEqual({ minScore: 4, maxScore: 6 });
        });

        test('returns correct range for medium risk posture (alias for moderate)', () => {
            const result = getRiskPostureScoreRange('medium', mockThresholds);

            expect(result).toStrictEqual({ minScore: 4, maxScore: 6 });
        });

        test('returns correct range for high risk posture', () => {
            const result = getRiskPostureScoreRange('high', mockThresholds);

            expect(result).toStrictEqual({ minScore: 7, maxScore: 10 });
        });

        test('returns correct range for critical risk posture', () => {
            const result = getRiskPostureScoreRange('critical', mockThresholds);

            expect(result).toStrictEqual({ minScore: 7, maxScore: 10 });
        });
    });

    describe('case sensitivity', () => {
        test('handles uppercase input', () => {
            const result = getRiskPostureScoreRange('LOW', mockThresholds);

            expect(result).toStrictEqual({ minScore: 0, maxScore: 3 });
        });

        test('handles mixed case input', () => {
            const result = getRiskPostureScoreRange('MoDeRaTe', mockThresholds);

            expect(result).toStrictEqual({ minScore: 4, maxScore: 6 });
        });

        test('handles lowercase input', () => {
            const result = getRiskPostureScoreRange('critical', mockThresholds);

            expect(result).toStrictEqual({ minScore: 7, maxScore: 10 });
        });
    });

    describe('edge cases', () => {
        test('returns empty object for empty risk posture', () => {
            const result = getRiskPostureScoreRange('', mockThresholds);

            expect(result).toStrictEqual({});
        });

        test('returns empty object for empty thresholds', () => {
            const result = getRiskPostureScoreRange('low', []);

            expect(result).toStrictEqual({});
        });

        test('returns empty object for unknown risk posture', () => {
            const result = getRiskPostureScoreRange('unknown', mockThresholds);

            expect(result).toStrictEqual({});
        });

        test('returns empty object for whitespace-only risk posture', () => {
            const result = getRiskPostureScoreRange('   ', mockThresholds);

            expect(result).toStrictEqual({});
        });
    });

    describe('different threshold configurations', () => {
        test('works with 2 thresholds', () => {
            const twoThresholds: RiskThresholdResponseDto[] = [
                {
                    id: 1,
                    minThreshold: 0,
                    maxThreshold: 5,
                    color: 'green500',
                    name: 'Low',
                    description: 'Low risk threshold',
                },
                {
                    id: 2,
                    minThreshold: 6,
                    maxThreshold: 10,
                    color: 'red500',
                    name: 'Critical',
                    description: 'Critical risk threshold',
                },
            ];

            expect(
                getRiskPostureScoreRange('low', twoThresholds),
            ).toStrictEqual({
                minScore: 0,
                maxScore: 5,
            });
            expect(
                getRiskPostureScoreRange('critical', twoThresholds),
            ).toStrictEqual({
                minScore: 6,
                maxScore: 10,
            });
            // moderate should map to the last threshold when there are only 2
            expect(
                getRiskPostureScoreRange('moderate', twoThresholds),
            ).toStrictEqual({
                minScore: 6,
                maxScore: 10,
            });
            // high should also map to the last threshold when there are only 2
            expect(
                getRiskPostureScoreRange('high', twoThresholds),
            ).toStrictEqual({
                minScore: 6,
                maxScore: 10,
            });
        });

        test('works with 4 thresholds', () => {
            const fourThresholds: RiskThresholdResponseDto[] = [
                {
                    id: 1,
                    minThreshold: 0,
                    maxThreshold: 2,
                    color: 'green500',
                    name: 'Low',
                    description: 'Low risk threshold',
                },
                {
                    id: 2,
                    minThreshold: 3,
                    maxThreshold: 5,
                    color: 'yellow500',
                    name: 'Moderate',
                    description: 'Moderate risk threshold',
                },
                {
                    id: 3,
                    minThreshold: 6,
                    maxThreshold: 8,
                    color: 'orange500',
                    name: 'High',
                    description: 'High risk threshold',
                },
                {
                    id: 4,
                    minThreshold: 9,
                    maxThreshold: 10,
                    color: 'red500',
                    name: 'Critical',
                    description: 'Critical risk threshold',
                },
            ];

            expect(
                getRiskPostureScoreRange('low', fourThresholds),
            ).toStrictEqual({
                minScore: 0,
                maxScore: 2,
            });
            expect(
                getRiskPostureScoreRange('moderate', fourThresholds),
            ).toStrictEqual({
                minScore: 3,
                maxScore: 5,
            });
            expect(
                getRiskPostureScoreRange('high', fourThresholds),
            ).toStrictEqual({
                minScore: 6,
                maxScore: 8,
            });
            expect(
                getRiskPostureScoreRange('critical', fourThresholds),
            ).toStrictEqual({
                minScore: 9,
                maxScore: 10,
            });
        });

        test('works with 5 thresholds', () => {
            const fiveThresholds: RiskThresholdResponseDto[] = [
                {
                    id: 1,
                    minThreshold: 0,
                    maxThreshold: 1,
                    color: 'green500',
                    name: 'Very Low',
                    description: 'Very low risk threshold',
                },
                {
                    id: 2,
                    minThreshold: 2,
                    maxThreshold: 3,
                    color: 'green300',
                    name: 'Low',
                    description: 'Low risk threshold',
                },
                {
                    id: 3,
                    minThreshold: 4,
                    maxThreshold: 6,
                    color: 'yellow500',
                    name: 'Moderate',
                    description: 'Moderate risk threshold',
                },
                {
                    id: 4,
                    minThreshold: 7,
                    maxThreshold: 8,
                    color: 'orange500',
                    name: 'High',
                    description: 'High risk threshold',
                },
                {
                    id: 5,
                    minThreshold: 9,
                    maxThreshold: 10,
                    color: 'red500',
                    name: 'Critical',
                    description: 'Critical risk threshold',
                },
            ];

            expect(
                getRiskPostureScoreRange('low', fiveThresholds),
            ).toStrictEqual({
                minScore: 0,
                maxScore: 1,
            });
            expect(
                getRiskPostureScoreRange('moderate', fiveThresholds),
            ).toStrictEqual({
                minScore: 2,
                maxScore: 3,
            });
            expect(
                getRiskPostureScoreRange('high', fiveThresholds),
            ).toStrictEqual({
                minScore: 4,
                maxScore: 6,
            });
            expect(
                getRiskPostureScoreRange('critical', fiveThresholds),
            ).toStrictEqual({
                minScore: 9,
                maxScore: 10,
            });
        });
    });

    describe('threshold sorting', () => {
        test('handles unsorted thresholds correctly', () => {
            const unsortedThresholds: RiskThresholdResponseDto[] = [
                {
                    id: 3,
                    minThreshold: 7,
                    maxThreshold: 10,
                    color: 'red500',
                    name: 'High',
                    description: 'High risk threshold',
                },
                {
                    id: 1,
                    minThreshold: 0,
                    maxThreshold: 3,
                    color: 'green500',
                    name: 'Low',
                    description: 'Low risk threshold',
                },
                {
                    id: 2,
                    minThreshold: 4,
                    maxThreshold: 6,
                    color: 'yellow500',
                    name: 'Moderate',
                    description: 'Moderate risk threshold',
                },
            ];

            // Should still work correctly despite unsorted input
            expect(
                getRiskPostureScoreRange('low', unsortedThresholds),
            ).toStrictEqual({
                minScore: 0,
                maxScore: 3,
            });
            expect(
                getRiskPostureScoreRange('moderate', unsortedThresholds),
            ).toStrictEqual({
                minScore: 4,
                maxScore: 6,
            });
            expect(
                getRiskPostureScoreRange('high', unsortedThresholds),
            ).toStrictEqual({
                minScore: 7,
                maxScore: 10,
            });
        });
    });
});
