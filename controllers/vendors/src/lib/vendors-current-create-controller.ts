import { isEmpty, isNaN } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { vendorsControllerCreateVendorMutation } from '@globals/api-sdk/queries';
import type { VendorRequestDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import type { FormValues } from '@ui/forms';
import { CREATING_VENDOR_ERROR } from './constants/vendors-create-snackbars.constants';

class VendorsCreateCurrentVendorController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorDetails: FormValues | null = null;
    vendorInternalDetails: FormValues | null = null;
    vendorImpactAssessmentDetails: FormValues | null = null;
    vendorId: number | null = null;
    vendorName = '';

    createVendorMutation = new ObservedMutation(
        vendorsControllerCreateVendorMutation,
    );

    get isLoading(): boolean {
        return this.createVendorMutation.isPending;
    }

    saveVendorDetails = (formValues: FormValues) => {
        const passwordPolicyGroup = formValues.passwordPolicyGroup as
            | FormValues
            | undefined;
        const passwordPolicy = passwordPolicyGroup?.passwordPolicy as
            | FormValues
            | undefined;

        const passwordMinLength = passwordPolicyGroup?.passwordMinLength as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            name: formValues.name ?? undefined,
            url: isEmpty(formValues.url) ? undefined : formValues.url,
            servicesProvided: isEmpty(formValues.servicesProvided)
                ? undefined
                : formValues.servicesProvided,
            passwordPolicy: passwordPolicy?.value ?? undefined,
            passwordRequiresMinLength:
                passwordPolicyGroup?.passwordRequiresMinLength ?? false,
            passwordMinLength: isNaN(passwordMinLength?.value)
                ? undefined
                : Number(passwordMinLength?.value),
            passwordRequiresNumber:
                passwordPolicyGroup?.passwordRequiresNumber ?? false,
            passwordRequiresSymbol:
                passwordPolicyGroup?.passwordRequiresSymbol ?? false,
            passwordMfaEnabled:
                passwordPolicyGroup?.passwordMfaEnabled ?? false,
            privacyUrl: isEmpty(formValues.privacyUrl)
                ? undefined
                : formValues.privacyUrl,
            termsUrl: isEmpty(formValues.termsUrl)
                ? undefined
                : formValues.termsUrl,
            contactAtVendor: isEmpty(formValues.contactAtVendor)
                ? undefined
                : formValues.contactAtVendor,
            contactsEmail: isEmpty(formValues.contactsEmail)
                ? undefined
                : formValues.contactsEmail,
        };

        this.vendorDetails = newValues;
    };

    saveVendorInternalDetails = (formValues: FormValues) => {
        const integrations = formValues.integrations as
            | ListBoxItemData[]
            | undefined;

        const integrationsIds = integrations?.map((integration) => {
            return isNaN(integration.id) ? undefined : Number(integration.id);
        });

        const status = formValues.status as ListBoxItemData | undefined;
        const type = formValues.type as ListBoxItemData | undefined;
        const category = formValues.category as ListBoxItemData | undefined;
        const risk = formValues.risk as ListBoxItemData | undefined;
        const user = formValues.user as ListBoxItemData | undefined;

        const newValues: FormValues = {
            status: status?.value ?? undefined,
            type: type?.value ?? undefined,
            category: category?.value ?? undefined,
            risk: risk?.value ?? undefined,
            impactLevel: formValues.impactLevel ?? undefined,
            dataStored: formValues.dataStored ?? undefined,
            hasPii: formValues.hasPii ?? undefined,
            isSubProcessor: formValues.isSubProcessor ?? undefined,
            location: formValues.location ?? undefined,
            integrations: integrationsIds ?? undefined,
            fullIntegrations: integrations ?? undefined,
            userId: isNaN(user?.id) ? undefined : Number(user?.id),
            user: user ?? undefined,
            contact: formValues.contact ?? undefined,
            cost: isNaN(formValues.cost) ? undefined : formValues.cost,
            notes: formValues.notes ?? undefined,
        };

        this.vendorInternalDetails = newValues;
    };

    saveImpactAssessmentDetails = (formValues: FormValues) => {
        const impactLevel = formValues.impactLevel as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            dataAccessedOrProcessedList: isEmpty(
                formValues.dataAccessedOrProcessedList,
            )
                ? undefined
                : formValues.dataAccessedOrProcessedList,
            operationalImpact: isEmpty(formValues.operationalImpact)
                ? undefined
                : formValues.operationalImpact,
            environmentAccess: isEmpty(formValues.environmentAccess)
                ? undefined
                : formValues.environmentAccess,
            impactLevel: impactLevel?.value ?? undefined,
        };

        this.createCurrentVendorDetails(newValues);
    };

    createCurrentVendorDetails = (newValues: FormValues) => {
        const mutatedVendorDetails = {
            ...this.vendorDetails,
            ...this.vendorInternalDetails,
            ...newValues,
            isSubProcessorActive: false,
            confirmed: true,
        } as VendorRequestDto;

        this.createVendorMutation.mutate({
            body: mutatedVendorDetails,
        });

        when(
            () => !this.createVendorMutation.isPending,
            () => {
                const { response, hasError } = this.createVendorMutation;

                if (hasError) {
                    snackbarController.addSnackbar(CREATING_VENDOR_ERROR);
                }
                if (response?.id) {
                    this.vendorId = response.id;
                    this.vendorName = response.name;
                }
            },
        );
    };
}

export const sharedVendorsCreateCurrentVendorController =
    new VendorsCreateCurrentVendorController();
