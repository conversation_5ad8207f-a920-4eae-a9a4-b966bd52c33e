import { isEmpty, isNil } from 'lodash-es';
import { sharedMonitorsController } from '@controllers/monitors';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type {
    ControlTestResponseDto,
    MonitorV2ControlTestInstanceOverviewResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, reaction, when } from '@globals/mobx';
import type { MonitorDetailType, MonitoringDataType } from './types';

class WorkspaceMonitorsCoordinatorController {
    constructor() {
        this.monitorDetails = {};
        makeAutoObservable(this);
    }

    monitorTestIds: number[] = [];

    monitorDetails: Record<number, MonitorDetailType> = {};

    get isLoading(): boolean {
        const isMonitorsLoading =
            sharedMonitorsController.monitorsQuery.isLoading;

        const { isOverviewLoading } = sharedWorkspaceMonitorsController;

        const areDetailsLoading = Object.values(this.monitorDetails).some(
            (detail) => detail.isLoading,
        );

        const allTestIdsLoaded =
            !isEmpty(this.monitorTestIds) &&
            this.monitorTestIds.every(
                (testId) =>
                    !isNil(this.monitorDetails[testId]) &&
                    !this.monitorDetails[testId].isLoading &&
                    this.monitorDetails[testId].overview !== null,
            );

        const pendingTestIds =
            !isEmpty(this.monitorTestIds) && !allTestIdsLoaded;

        return (
            isMonitorsLoading ||
            isOverviewLoading ||
            areDetailsLoading ||
            pendingTestIds
        );
    }

    get allMonitorsData() {
        return sharedMonitorsController.monitors;
    }

    get combinedMonitoringData(): MonitoringDataType[] {
        return Object.values(this.monitorDetails)
            .filter((detail) => detail.overview && !detail.isLoading)
            .map((detail) => this.transformToMonitoringData(detail));
    }

    setControlId(controlId: number | null): void {
        sharedMonitorsController.setControlId(controlId);
    }

    loadMonitors(params?: FetchDataResponseParams): void {
        sharedMonitorsController.loadMonitors(params);

        reaction(
            () => sharedMonitorsController.monitors,
            (monitors) => {
                this.setMonitorsTestIds(monitors);
            },
        );
    }

    get allMonitorTestIds(): number[] {
        return this.monitorTestIds;
    }

    setMonitorsTestIds(monitors: ControlTestResponseDto[]) {
        const testIds = monitors.map((monitor) => monitor.testId);

        this.monitorTestIds = testIds;
        this.loadedTestIds.clear();

        this.loadMonitorOverviewSequentially(testIds, 0);
    }

    loadMonitorOverviewSequentially(testIds: number[], index: number): void {
        if (index >= testIds.length) {
            console.warn('index cannot be greater than testIds length');

            return;
        }

        const testId = testIds[index];

        this.loadMonitorOverview(testId);

        setTimeout(() => {
            this.loadMonitorOverviewSequentially(testIds, index + 1);
        }, 300);
    }

    loadedTestIds: Set<number> = new Set();

    loadMonitorOverview(testId: number): void {
        this.monitorDetails[testId] = {
            testId,
            overview: null,
            isLoading: true,
        };

        if (this.loadedTestIds.has(testId)) {
            return;
        }

        this.loadedTestIds.add(testId);

        sharedWorkspaceMonitorsController.loadWorkspaceMonitorTestOverview(
            testId,
        );

        reaction(
            () => ({
                overview:
                    sharedWorkspaceMonitorsController.workspaceMonitorTestOverview,
                currentOverviewTestId:
                    sharedWorkspaceMonitorsController.currentOverviewTestId,
                overviewLoading:
                    sharedWorkspaceMonitorsController
                        .workspaceMonitorTestOverviewQuery.isLoading,
            }),
            (result) => {
                const { overview, currentOverviewTestId, overviewLoading } =
                    result;

                const testIdMatches = currentOverviewTestId === testId;

                when(
                    () => !overviewLoading,
                    () => {
                        if (testIdMatches) {
                            if (overview) {
                                this.updateMonitorDetail(testId, overview);
                            } else {
                                console.error(
                                    `Error loading overview for test ID ${testId}`,
                                );
                                this.monitorDetails[testId].isLoading = false;
                            }
                        }
                    },
                );
            },
            { fireImmediately: true },
        );
    }

    updateMonitorDetail(
        testId: number,
        overview: MonitorV2ControlTestInstanceOverviewResponseDto,
    ): void {
        if (this.monitorTestIds.includes(testId)) {
            this.monitorDetails[testId] = {
                ...this.monitorDetails[testId],
                overview,
                isLoading: false,
            };
        }
    }

    transformToMonitoringData(detail: MonitorDetailType): MonitoringDataType {
        const { overview, testId } = detail;

        if (isNil(overview)) {
            throw new Error('Overview is null');
        }

        return {
            productId: 1,
            testId,
            testName: overview.name,
            checkResultStatus: overview.checkResultStatus,
            checkTypes: overview.checkTypes,
            availableConnections: overview.availableConnections,
            draft: overview.draft,
        };
    }
}

export const sharedWorkspaceMonitorsCoordinatorController =
    new WorkspaceMonitorsCoordinatorController();
