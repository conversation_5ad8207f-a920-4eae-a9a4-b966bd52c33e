import { complianceCheckExclusionsControllerGetComplianceCheckExclusionOptions } from '@globals/api-sdk/queries';
import type { ComplianceCheckExclusionResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class ComplianceCheckExclusionDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    #exclusionDetailsQuery = new ObservedQuery(
        complianceCheckExclusionsControllerGetComplianceCheckExclusionOptions,
    );

    get isLoading(): boolean {
        return this.#exclusionDetailsQuery.isLoading;
    }

    get hasError(): boolean {
        return this.#exclusionDetailsQuery.hasError;
    }

    get exclusionDetails(): ComplianceCheckExclusionResponseDto | null {
        return this.#exclusionDetailsQuery.data ?? null;
    }

    loadExclusionDetails = (exclusionId: number): void => {
        this.#exclusionDetailsQuery.load({
            path: { id: exclusionId },
        });
    };
}

export const sharedComplianceCheckExclusionDetailsController =
    new ComplianceCheckExclusionDetailsController();
