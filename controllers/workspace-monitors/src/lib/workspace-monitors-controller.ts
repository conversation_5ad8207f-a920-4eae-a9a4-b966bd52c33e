import { monitorsV2ControllerGetControlTestInstanceOverviewOptions } from '@globals/api-sdk/queries';
import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class WorkspaceMonitorsController {
    constructor() {
        makeAutoObservable(this);
    }

    currentOverviewTestId: number | null = null;

    workspaceMonitorTestOverviewQuery = new ObservedQuery(
        monitorsV2ControllerGetControlTestInstanceOverviewOptions,
    );

    loadWorkspaceMonitorTestOverview = (testId: number): void => {
        const { currentWorkspaceId, isLoading } = sharedWorkspacesController;

        when(
            () => !isLoading,
            () => {
                if (!currentWorkspaceId) {
                    return;
                }

                this.currentOverviewTestId = testId;
                this.workspaceMonitorTestOverviewQuery.load({
                    path: { testId, workspaceId: currentWorkspaceId },
                });
            },
        );
    };

    get workspaceMonitorTestOverview(): MonitorV2ControlTestInstanceOverviewResponseDto | null {
        return this.workspaceMonitorTestOverviewQuery.data;
    }

    get isOverviewLoading(): boolean {
        return this.workspaceMonitorTestOverviewQuery.isLoading;
    }
}

export const sharedWorkspaceMonitorsController =
    new WorkspaceMonitorsController();
