import type { MonitorTrackStatus } from '../types/monitor-track-response.type';

export const mapMonitorTrackStatus = (status: number): MonitorTrackStatus => {
    switch (status) {
        case 1: {
            return 'READY';
        }
        case 2: {
            return 'PASSED';
        }
        case 3: {
            return 'FAILED';
        }
        case 4: {
            return 'ERROR';
        }
        case 5: {
            return 'PREAUDIT';
        }
        default: {
            return 'READY';
        }
    }
};

export const mapStatusToTrackStatus = (
    status: MonitorTrackStatus,
): number | undefined => {
    switch (status) {
        case 'READY': {
            return 1;
        }
        case 'PASSED': {
            return 2;
        }
        case 'FAILED': {
            return 3;
        }
        case 'ERROR': {
            return 4;
        }
        case 'PREAUDIT': {
            return 5;
        }
        default: {
            return undefined;
        }
    }
};
