import { isNil } from 'lodash-es';
import { monitorsControllerGetControlTestHistoryOptions } from '@globals/api-sdk/queries';
import type { ControlTestInstanceHistoryResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class MonitoringHistoryController {
    constructor() {
        makeAutoObservable(this);
    }

    monitoringHistoryQuery = new ObservedQuery(
        monitorsControllerGetControlTestHistoryOptions,
    );

    get monitoringHistoryData(): ControlTestInstanceHistoryResponseDto | null {
        return this.monitoringHistoryQuery.data ?? null;
    }

    get isLoading(): boolean {
        return this.monitoringHistoryQuery.isLoading;
    }

    loadMonitoringHistory = (testId: number) => {
        const { currentWorkspaceId } = sharedWorkspacesController;

        when(
            () => !isNil(currentWorkspaceId),
            () => {
                if (!currentWorkspaceId) {
                    return;
                }

                this.monitoringHistoryQuery.load({
                    path: {
                        xProductId: currentWorkspaceId,
                    },
                    query: {
                        testId,
                        reportInterval: 'MONTHLY',
                    },
                });
            },
        );
    };
}

export const sharedMonitoringHistoryController =
    new MonitoringHistoryController();
