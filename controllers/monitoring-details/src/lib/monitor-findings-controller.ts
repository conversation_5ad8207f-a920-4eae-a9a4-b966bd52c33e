import { monitorsV2ControllerGetFindingsOptions } from '@globals/api-sdk/queries';
import type { MonitorV2FindingsResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class MonitorFindingsController {
    constructor() {
        makeAutoObservable(this);
    }

    findingsResponse = new ObservedQuery(
        monitorsV2ControllerGetFindingsOptions,
    );

    get ticketsFindings(): MonitorV2FindingsResponseDto | null {
        return this.findingsResponse.data ?? null;
    }

    get failingResources(): number {
        return this.ticketsFindings?.failingResourcesCount ?? 0;
    }

    get isLoading(): boolean {
        return this.findingsResponse.isLoading;
    }

    get monitorHasFindings(): boolean {
        return this.failingResources > 0;
    }

    loadFindingsResponse = (testId: number): void => {
        const { currentWorkspaceId, isLoading } = sharedWorkspacesController;

        when(
            () => !isLoading,
            () => {
                if (!currentWorkspaceId) {
                    console.error('No workspace selected');

                    return;
                }

                this.findingsResponse.load({
                    path: { testId, workspaceId: currentWorkspaceId },
                });
            },
        );
    };
}

export const sharedMonitorFindingsController = new MonitorFindingsController();
