import { monitorsV2ControllerGetTrackOptions } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { mapMonitorTrackStatus } from '../helpers/map-monitor-track-status.helper';
import type { MonitorTrackResponse } from '../types/monitor-track-response.type';

class TrackCardController {
    constructor() {
        makeAutoObservable(this);
    }

    monitorTrackResponse = new ObservedQuery(
        monitorsV2ControllerGetTrackOptions,
    );

    get trackData(): MonitorTrackResponse | null {
        return {
            consecutiveDays:
                this.monitorTrackResponse.data?.consecutiveDays ?? 0,
            status: mapMonitorTrackStatus(
                this.monitorTrackResponse.data?.status ?? 1,
            ),
            startDateWithStatus:
                this.monitorTrackResponse.data?.startDateWithStatus ?? null,
        };
    }

    get isLoading(): boolean {
        return this.monitorTrackResponse.isLoading;
    }

    loadTrackData = (testId: number) => {
        const { currentWorkspaceId, isLoading } = sharedWorkspacesController;

        when(
            () => !isLoading,
            () => {
                if (!currentWorkspaceId) {
                    return;
                }

                this.monitorTrackResponse.load({
                    path: { testId, workspaceId: currentWorkspaceId },
                });
            },
        );
    };
}

export const activeTrackCardController = new TrackCardController();
