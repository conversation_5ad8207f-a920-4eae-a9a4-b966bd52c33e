import {
    monitorsV2ControllerGetTicketsMetadataOptions,
    monitorsV2ControllerGetTicketsOptions,
} from '@globals/api-sdk/queries';
import type {
    MonitorV2TicketsMetadataResponseDto,
    TicketResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class TicketsMetadataController {
    monitorTicketsMetadataQuery = new ObservedQuery(
        monitorsV2ControllerGetTicketsMetadataOptions,
    );

    monitorTicketsQuery = new ObservedQuery(
        monitorsV2ControllerGetTicketsOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get ticketsMetadata(): MonitorV2TicketsMetadataResponseDto | null {
        return this.monitorTicketsMetadataQuery.data ?? null;
    }

    get ticketMetadata(): TicketResponseDto[] {
        if (!this.monitorTicketsQuery.data) {
            return [];
        }

        return this.monitorTicketsQuery.data.data;
    }

    get ticketsInProgress(): number {
        return this.ticketsMetadata?.inProgress ?? 0;
    }

    get totalTickets(): number {
        return this.ticketMetadata.length + this.ticketsInProgress;
    }

    loadTicketsMetadata = (testId: number) => {
        when(
            () => !sharedWorkspacesController.isLoading,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                this.monitorTicketsMetadataQuery.load({
                    path: { testId, workspaceId: currentWorkspace?.id ?? 1 },
                });
                this.monitorTicketsQuery.load({
                    path: { testId, workspaceId: currentWorkspace?.id ?? 1 },
                    query: { isCompleted: true },
                });
            },
        );
    };
}

export const activeTicketsMetadataController = new TicketsMetadataController();
