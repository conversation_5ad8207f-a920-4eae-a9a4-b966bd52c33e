import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { monitorsV2ControllerGetMonitorControlsOptions } from '@globals/api-sdk/queries';
import type { ControlMonitorResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class MonitoringDetailsControlsController {
    constructor() {
        makeAutoObservable(this, { monitoringDetailsControls: false });
    }

    monitoringDetailsControls = new ObservedQuery(
        monitorsV2ControllerGetMonitorControlsOptions,
    );

    get monitoringDetailsControlsData(): ControlMonitorResponseDto[] {
        return this.monitoringDetailsControls.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.monitoringDetailsControls.isLoading;
    }

    get monitoringDetailsControlsTotal(): number {
        return this.monitoringDetailsControls.data?.total || 0;
    }

    loadMonitoringDetailsControls = (
        testId: number,
        params?: FetchDataResponseParams,
    ) => {
        const { currentWorkspace, isLoading } = sharedWorkspacesController;

        when(
            () => !isLoading,
            () => {
                if (!currentWorkspace) {
                    return;
                }

                const query = {
                    page: params?.pagination.page ?? 1,
                    limit: params?.pagination.pageSize ?? DEFAULT_PAGE_SIZE,
                };

                this.monitoringDetailsControls.load({
                    query,
                    path: {
                        testId,
                        workspaceId: currentWorkspace.id,
                    },
                });
            },
        );
    };
}

export const sharedMonitoringDetailsControlsController =
    new MonitoringDetailsControlsController();
