import { useEffect, useRef } from 'react';

/**
 * Hook that observes an element's height and sets it as a CSS variable.
 *
 */
export function useElementHeightAsCSSVar({
    cssVariableName,
}: {
    cssVariableName: string;
}): React.RefObject<HTMLDivElement> {
    const elementRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (!elementRef.current) {
            return;
        }

        const updateElementHeight = () => {
            const height = elementRef.current?.offsetHeight || 0;

            document.documentElement.style.setProperty(
                `--${cssVariableName}`,
                `${height}px`,
            );
        };

        // Initial measurement
        updateElementHeight();

        // Create ResizeObserver to track size changes
        const resizeObserver = new ResizeObserver(updateElementHeight);

        resizeObserver.observe(elementRef.current);

        return () => {
            resizeObserver.disconnect();
            // Remove the CSS variable when component unmounts
            document.documentElement.style.removeProperty(
                `--${cssVariableName}`,
            );
        };
    }, [cssVariableName]);

    return elementRef;
}
