import type { ReactElement, RefObject } from 'react';
import type {
    Action,
    ButtonAction,
    DropdownAction,
} from '@cosmos/components/action-stack';
import type { ButtonProps } from '@cosmos/components/button';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import type { SchemaDropdownProps } from '@cosmos/components/schema-dropdown';
import type { TooltipProps } from '@cosmos/components/tooltip';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type {
    ColumnDef,
    CoreOptions,
    PaginationOptions,
    Row,
    RowData,
    RowSelectionOptions,
    SortingState,
} from '@tanstack/react-table';
import type { DatatableRef } from './datatable-ref.type';
import type { DensityOption } from './density-option.type';
import type { ViewModeType } from './enabled-views.type';
import type { ExtendedPaginationState } from './extended-pagination-state.type';
import type { FetchDataResponseParams } from './fetch-data-response-params.type';
import type { FilterProps } from './filter-props.type';
import type { FilterViewModeProps } from './filter-view-mode-props.type';
import type { OnRowSelection } from './on-row-selection.type';
import type { TableSearchProps } from './table-search-props.type';
import type { TableSettingsTriggerProps } from './table-settings-trigger-props.type';

type BulkActionButton = Pick<
    ButtonProps,
    'data-id' | 'label' | 'level' | 'onClick' | 'startIconName'
>;

export interface ButtonBulkAction
    extends Omit<Action, 'actionType' | 'typeProps'> {
    actionType: 'button';
    typeProps: BulkActionButton;
}

export interface DropdownBulkAction
    extends Omit<Action, 'actionType' | 'typeProps'> {
    actionType: 'dropdown';
    typeProps: Pick<
        SchemaDropdownProps,
        'data-id' | 'items' | 'label' | 'level' | 'onSelectGlobalOverride'
    >;
}

export interface TooltipButtonProps {
    tooltip: Omit<TooltipProps, 'children'>;
    button: BulkActionButton;
}

export interface TooltipButtonBulkAction
    extends Omit<Action, 'actionType' | 'typeProps'> {
    actionType: 'tooltipButton';
    typeProps: TooltipButtonProps;
}

/* NOTE: Updating this type? Make sure to update the docs:
 * /storybook/constants/arg-types/details/bulk-actions-type-detail.constant.ts */
export type BulkAction =
    | DropdownBulkAction
    | ButtonBulkAction
    | TooltipButtonBulkAction;

/* NOTE: Updating this type? Make sure to update the docs:
 * /storybook/docs/table-actions/constants/table-actions-type-detail.constant.ts */
export type TableAction = Omit<Action, 'actionType' | 'typeProps'> &
    (ButtonAction | DropdownAction);

export type ExtendedDataTableColumnDef<
    TData extends RowData,
    TValue = unknown,
> = ColumnDef<TData, TValue> & {
    isActionColumn?: boolean;
};

/**
 * Base interface for Datatable props without conditional requirements.
 */
interface BaseDatatableProps<TData extends RowData> {
    /**
     * An array of definitions that specifies multiple bulk actions that will be available in a dropdown menu when rows are selected in the Datatable.
     */
    bulkActionDropdownItems?: BulkAction[];
    /**
     * An array of column definitions that specify how each column in the datatable should be rendered and behave.
     */
    columns: ExtendedDataTableColumnDef<TData>[];
    /** The data to be rendered in the table.
     *
     * See [Data](/?path=/docs/unstable-components-datatable-data--docs) for more details.
     */
    data: TData[];
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Options to be applied to all columns.  These options can be overridden by the definitions in the `columns` prop.
     */
    defaultColumnOptions?: {
        minSize?: number;
        maxSize?: number;
    };
    /**
     * Default server-side pagination options such as page size, index of the displayed page, and available page size options.
     */
    defaultPaginationOptions?: ExtendedPaginationState;
    /** An object containing configuration properties for the Datatable component's empty state view.
     * See the [EmptyState component](/?path=/docs/components-emptystate--docs) for more details.
     */
    emptyStateProps?: EmptyStateProps;
    /** An object containing configuration properties for the Datatable component's filtering functionality.
     *
     * See [Filters](/?path=/docs/unstable-components-datatable-filters--docs) for more details.
     */
    filterProps?: FilterProps;
    /** An object containing configuration properties for the view mode of Filters.
     * See [Filters](/?path=/docs/unstable-components-datatable-filters--docs) for more details.
     */
    filterViewModeProps?: FilterViewModeProps;
    /**
     * Customize the text of the "Select All Rows" bulk action button.
     */
    getSelectAllButtonText?: ({
        rowCount,
    }: {
        rowCount: PaginationOptions['rowCount'];
    }) => string;
    /**
     * The initial sorting directive for the table.
     */
    initialSorting?: SortingState;
    /**
     * If `true`, multiple rows can be selected at once. If `false`, only single row selection is allowed.
     */
    isMultiRowSelectionEnabled?: boolean;
    /**
     * If `true`, the loading state of the table will be shown.
     */
    isLoading: boolean;
    /**
     * If `true`, the sorting carets will be rendered in the header.
     */
    isSortable?: boolean;
    /**
     * A unique identifier for the table, used to create ids for subcomponents such as filters.
     */
    tableId: string;
    /** Definitions for the [ActionStack](/?path=/docs/unstable-components-actionstack--docs) rendered above the table.
     *
     * See [Table Actions](/?path=/docs/unstable-components-datatable-table-actions--docs) for more details.
     */
    tableActions?: TableAction[];
    /**
     * Settings for the search functionality.
     */
    tableSearchProps?: TableSearchProps;
    /**
     * An Action definition for table settings.  May be a dropdown or a button.  Renders to the right of the search input.
     */
    tableSettingsTriggerProps?: TableSettingsTriggerProps;
    /**
     * The total number of rows in the current dataset, used for pagination.
     */
    total: number;
    /**
     * Callback that runs on mount, filter, search, and page change.
     */
    onFetchData?: (params: FetchDataResponseParams) => void;
    /**
     * Callback that is called when a row body is clicked.  Not called when rows are selected or deselected.
     */
    onRowClick?: ({
        row,
        _internal,
    }: {
        row: TData;
        /** The TanStack table row object that provides access to internal table functionality for the row. */
        _internal: Row<TData>;
    }) => void;
    /**
     * Callback that runs when rows are selected or deselected.
     */
    onRowSelection?: OnRowSelection;
    /**
     * Text to show in a tooltip when a checkbox is disable, if this does not send then tooltip does not show.
     */
    disabledRowSelectionCheckboxTooltip?: string;
    /**
     * Custom ref allowing the parent component to interact with the internal Datatable functions such as setPage.
     */
    imperativeHandleRef?: RefObject<DatatableRef>;
    /**
     * Enabled views to show alongside table view (e.g allowing gallery view).
     */
    viewMode?: ViewModeType;
    /**
     * Card component to be rendered when gallery view is activated.
     */
    galleryCard?: React.ComponentType<{
        row: TData;
    }>;
    /**
     * Custom skeleton card component to be rendered when gallery is in loading state.
     */
    galleryCustomSkeletonCard?: ReactElement;
    /**
     * If `true`, the pagination controls will be hidden.
     */
    hidePagination?: boolean;
    /**
     * If `true`, the total count of rows will not be displayed in the pagination controls.
     */
    cantDetermineTotalCount?: boolean;
    /**
     * Controls the density of the table cells.
     */
    density?: DensityOption;
    /**
     * If `true`, the table will be rendered as a full page table with specific styling and behavior.
     */
    isFullPageTable?: boolean;
}

/**
 * Conditional type that makes getRowId required when isRowSelectionEnabled is provided.
 *
 * 🚨 BREAKING CHANGE NOTICE 🚨
 * If you're using `isRowSelectionEnabled`, you MUST now provide a `getRowId` function.
 * This ensures row selection works correctly by providing unique identifiers for each row.
 *
 * Example implementations:
 * - For objects with string id: `getRowId: (row) => row.id`
 * - For objects with number id: `getRowId: (row) => String(row.id)`
 * - For objects with uuid: `getRowId: (row) => row.uuid`
 * - For arrays with index: `getRowId: (row, index) => String(index)`.
 *
 * See the migration guide for more details: cosmos/components/datatable/DATATABLE_GETROWID_MIGRATION_GUIDE.md.
 */
export type DatatableProps<TData extends RowData> = BaseDatatableProps<TData> &
    (
        | {
              /**
               * If isRowSelectionEnabled is either true or a function, treat it as if row selection is ENABLED
               * if isRowSelectionEnabled is false or undefined, treat it as if row selection is DISABLED.
               *
               * ⚠️ REQUIRED: When using row selection, you MUST provide a `getRowId` function
               * to ensure proper row identification and selection state management.
               */
              isRowSelectionEnabled: RowSelectionOptions<TData>['enableRowSelection'];
              /**
               * Return a useful ID for row selection which will be stored in the `rowSelection` object when that row has been selected.
               *
               * 🔧 REQUIRED when isRowSelectionEnabled is provided.
               *
               * Examples:
               * - `getRowId: (row) => row.id` (for objects with string id property)
               * - `getRowId: (row) => String(row.id)` (for objects with number id property)
               * - `getRowId: (row) => row.uuid` (for objects with uuid property)
               * - `getRowId: (row, index) => String(index)` (fallback using array index).
               */
              getRowId: CoreOptions<TData>['getRowId'];
          }
        | {
              /**
               * If isRowSelectionEnabled is either true or a function, treat it as if row selection is ENABLED
               * if isRowSelectionEnabled is false or undefined, treat it as if row selection is DISABLED.
               */
              isRowSelectionEnabled?: false | undefined;
              /**
               * Return a useful ID for row selection which will be stored in the `rowSelection` object when that row has been selected.
               *
               * ℹ️ OPTIONAL when row selection is disabled.
               */
              getRowId?: CoreOptions<TData>['getRowId'];
          }
    );
