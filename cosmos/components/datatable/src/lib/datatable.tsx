import { DEFAULT_DATA_ID } from '@cosmos/components/field-feedback';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { RowData } from '@tanstack/react-table';
import { TableContent } from './components/table-content/TableContent';
import { DEFAULT_COLUMN_MAX_SIZE } from './constants/default-column-max-size.constant';
import { DEFAULT_COLUMN_MIN_SIZE } from './constants/default-column-min-size.constant';
import { DEFAULT_PAGE } from './constants/default-page.constant';
import { DEFAULT_PAGE_INDEX } from './constants/default-page-index.constant';
import { DEFAULT_PAGE_SIZE } from './constants/default-page-size.constant';
import { DEFAULT_PAGE_SIZE_OPTIONS } from './constants/default-page-size-options.constant';
import { DEFAULT_TABLE_SEARCH_PROPS } from './constants/default-table-search-props.constant';
import { FiltersViewModeProvider } from './context/filters-view-mode.provider';
import { useDatatable } from './hooks/use-datatable.hook';
import type { DatatableProps } from './types/datatable-props.type';

/**
 * Datatable is a structured component designed for displaying and interacting with large sets of data,
 * offering features like sorting, filtering, and bulk actions to facilitate comparison and analysis in a tabular format.
 *
 * 🚧 Needs Figma Link.
 */
export const Datatable = <TData extends RowData = RowData>({
    bulkActionDropdownItems = undefined,
    columns = [],
    data = [],
    'data-id': dataId = DEFAULT_DATA_ID,
    defaultColumnOptions = {
        minSize: DEFAULT_COLUMN_MIN_SIZE,
        maxSize: DEFAULT_COLUMN_MAX_SIZE,
    },
    defaultPaginationOptions = {
        page: DEFAULT_PAGE,
        pageIndex: DEFAULT_PAGE_INDEX,
        pageSize: DEFAULT_PAGE_SIZE,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    },
    density = 'normal',
    emptyStateProps = undefined,
    filterProps = undefined,
    filterViewModeProps = { viewMode: 'toggleable' },
    getRowId,
    getSelectAllButtonText = undefined,
    hidePagination = false,
    cantDetermineTotalCount = false,
    initialSorting = [],
    isRowSelectionEnabled = undefined,
    isMultiRowSelectionEnabled = true,
    isLoading = false,
    isSortable = true,
    onFetchData = undefined,
    onRowClick = undefined,
    onRowSelection = undefined,
    tableActions = undefined,
    tableId,
    tableSearchProps = DEFAULT_TABLE_SEARCH_PROPS,
    tableSettingsTriggerProps = undefined,
    total,
    disabledRowSelectionCheckboxTooltip = undefined,
    viewMode = 'table',
    galleryCard,
    galleryCustomSkeletonCard,
    imperativeHandleRef,
    isFullPageTable = false,
}: DatatableProps<TData>): React.JSX.Element => {
    const { table } = useDatatable<TData>({
        bulkActionDropdownItems,
        columns,
        data,
        defaultColumnOptions,
        defaultPaginationOptions,
        density,
        emptyStateProps,
        filterProps,
        filterViewModeProps,
        getRowId,
        getSelectAllButtonText,
        hidePagination,
        cantDetermineTotalCount,
        initialSorting,
        isRowSelectionEnabled,
        isMultiRowSelectionEnabled,
        isLoading,
        isSortable,
        onFetchData,
        onRowClick,
        onRowSelection,
        tableActions,
        tableId,
        tableSearchProps,
        tableSettingsTriggerProps,
        total,
        disabledRowSelectionCheckboxTooltip,
        viewMode,
        galleryCard,
        galleryCustomSkeletonCard,
        imperativeHandleRef,
        isFullPageTable,
    });

    return (
        <FiltersViewModeProvider<TData>
            table={table}
            data-testid="Datatable"
            data-id="jue1dnOo"
        >
            <TableContent data-id={dataId} table={table} />
        </FiltersViewModeProvider>
    );
};
