import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as DatatableStories from './Datatable.stories';
import sortingFiltering from './assets/sortingFiltering.png';
import twoDataPoint from './assets/twoDataPoint.png';
import pagination from './assets/pagination.png';
import search from './assets/search.png';
import bulkAction from './assets/bulkActions.png';
import rowActions from './assets/rowActions.png';
import dropdownActions from './assets/dropdownActions.png';
import actionStackAction from './assets/actionStackAction.png';
import toggleActions from './assets/toggleActions.png';
import tableActions from './assets/tableActions.png';

<Meta of={DatatableStories} />

<Title />

<Description />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Primary />

<Controls of={DatatableStories.Playground} />

## Table Density Options

The Datatable component supports three density options that control the height and padding of table cells:

- **compact**: 2px padding and 24px minimum height.
- **normal**: 8px padding and 48px minimum height.
- **spacious**: 16px padding and 64px minimum height.

The density can be controlled through the `density` prop or via the table settings menu.

<Canvas of={DatatableStories.Density} />

<Controls of={DatatableStories.Density} include={['density']} />

## Visibility Options

The Datatable component provides props to control which UI elements are shown:

- **hidePagination**: Hides the pagination controls at the bottom of the table
- **Top bar**: To hide the entire top bar, **do not** supply any of the following:
    - **tableSearchProps.hideSearch**: Set to `true` to hide the search input in the top bar
    - **filterProps**: Omit this props to hide the filters
    - **tableSettingsTriggerProps**: Omit this prop to hide the settings (gear) trigger
    - **tableActions**: Omit this prop to hide all action buttons

These options allow you to create a clean, minimal table when certain features aren't needed.

<Canvas of={DatatableStories.VisibilityOptions} />

<Controls of={DatatableStories.VisibilityOptions} include={['hidePagination', 'tableSearchProps.hideSearch']} />

## Datatable Dev Guides

- [Columns](/docs/unstable-components-datatable-columns--docs)
- [Data](/docs/unstable-components-datatable-data--docs)
- [Table Actions](/docs/unstable-components-datatable-table-actions--docs)
- [Filters](/docs/unstable-components-datatable-filters--docs)
- [Search](/docs/unstable-components-datatable-search--docs)

## 🟢 When to use the component

- When presenting large sets of structured data that require organization and comparison.
- When users need to perform actions such as sorting, filtering, and editing data within the table.
- When displaying data that benefits from being viewed in a tabular format for easy readability and analysis.
- When you need to provide an overview of multiple items and their properties.

## ❌ When not to use the component

- When the data set is small, it may be better displayed using simpler components like lists or cards.
- When the information requires complex visualizations such as charts or graphs.

## **Basic Setup**

    - Define the column order from most important to least important from left to right.
    - Add up to two data points per cell.

    <img
        src={twoDataPoint}
        alt="add control button picture"
        width="600px"
    />
    - Configure any additional features like sorting, filtering, pagination, search capability, and bulk actions.

## **Sorting and Filtering**

    - Enable sorting by setting the sortable property on columns. Only consider sorting where it’s most relevant to the user.

    <img
        src={sortingFiltering}
        alt="add control button picture"
        width="600px"
    />

## **Pagination**

    - Add pagination for large data sets. It’s important to establish the number of rows per page that are allowed.

    <img
        src={pagination}
        alt="add control button picture"
        width="600px"
    />

## **Setting the current page externally**

    - Use only when you need to update the current page externally due to dynamic table data or filtering
    - Keep in mind that pageIndex is 0 based
    - A basic example of how you can set the current page:

```tsx
import React, { useRef } from 'react';
import Datatable from './Datatable';

const ParentComponent = () => {
    const datatableRef = useRef();

    const handleSetPage = () => {
        if (datatableRef?.current) {
            datatableRef.current.setPageIndex(2); // Example to set page to 2
        }
    };

    return (
        <div>
            <button onClick={handleSetPage}>Set Page</button>
            <Datatable customRef={datatableRef} />
        </div>
    );
};

export default ParentComponent;
```

## **Search**

    - Add `TopBar` for search capabilities, especially for large data sets

    <img
        src={search}
        alt="add control button picture"
        width="600px"
    />

## **Table actions**

    - Always start with the most important bulk actions, arranged from left to right.
    - Limit the interface to a maximum of three buttons

    1. An optional primary button for main actions for the table.
    2. A secondary button for a less prominent action
    3. A tertiary ‘more’ button for less important actions stacked in a dropdown if needed.”

    <img
        src={tableActions}
        alt="table actions picture"
        width="600px"
    />

## **Row Selection and getRowId**

When enabling row selection with `isRowSelectionEnabled`, you **MUST** provide a `getRowId` function to ensure proper row identification and selection state management.

### ✅ Correct Usage

```tsx
<Datatable
    isRowSelectionEnabled
    getRowId={(row) => row.id} // Required when row selection is enabled
    data={myData}
    columns={columns}
    onRowSelection={({ selectedRows, isAllRowsSelected }) => {
        console.log('Selected rows:', selectedRows);
    }}
/>
```

### ❌ Incorrect Usage (Will Cause TypeScript Error)

```tsx
<Datatable
    isRowSelectionEnabled
    data={myData}
    columns={columns}
    // Missing getRowId - this will fail TypeScript compilation
/>
```

### Common getRowId Patterns

- **Objects with string id**: `getRowId={(row) => row.id}`
- **Objects with number id**: `getRowId={(row) => String(row.id)}`

> **⚠️ Important**: `getRowId` must return a string. If your data has numeric IDs, wrap them with `String()` like `getRowId={(row) => String(row.id)}`.

> **💡 Automatic Loading State Handling**: The Datatable has built-in protection for loading states! You can safely use simple property access like `getRowId={(row) => row.id}` without worrying about undefined properties during loading. The component automatically detects loading states and generates safe IDs like `loading-row-0`, `loading-row-1`, etc. This feature is built into the table and requires no additional setup.

### Why getRowId is Required

- ✅ Ensures correct row selection behavior
- ✅ Enables proper bulk actions functionality
- ✅ Provides consistent row identification across pagination

## **Bulk actions**

    - Always add a “Select” all button as the first bulk action in the `TopBar` .
    - Always start with the most important bulk actions, arranged from left to right. If there is insufficient space, you can use a `Dropdown` to add more actions to a list.

    <img
        src={bulkAction}
        alt="add control button picture"
        width="600px"
    />

## **Row actions**

    - Row actions are added at the end of the row, before the chevron which opens the Panel or AsidePiece.

    <img
        src={rowActions}
        alt="add control button picture"
        width="600px"
    />
    - Use secondary or tertiary button depending on what other actions are on the page.
    - Use a Dropdown component if there is more than one action with similar prominence per row.

    <img
        src={dropdownActions}
        alt="add control button picture"
        width="600px"
    />
    - Use an ActionStack component in case there is a need for a more prominent action among another group of actions on a row. The least prominent actions should be in a Dropdown

    <img
        src={actionStackAction}
        alt="add control button picture"
        width="600px"
    />
    - Use ToggleGroup for quick change of row “status”

    <img
        src={toggleActions}
        alt="add control button picture"
        width="600px"
    />

## **Height and width**

    - Columns will always adjust to the largest data within a cell. Each cell can have a predetermined size if necessary. (This size should be determined by product team)

## **Column alignment**

    - Header: The table header will always be aligned left to right and top to bottom by default. Only right-aligned cells will cause the header to be left-aligned
    - Cells: Left-aligned and top-aligned by default. Numbers can be right-aligned

## **Contained in a section or a card**

    - Use only when you have more than one section on a page
    - Don’t use pinned filters on cards
