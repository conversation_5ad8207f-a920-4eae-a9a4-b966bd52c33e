// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { CoreOptions, RowData } from '@tanstack/react-table';

/**
 * Creates a safe wrapper around the user's getRowId function that handles loading states automatically.
 *
 * During loading states, the Datatable creates fake rows filled with `0 as TData`, which causes
 * user's getRowId functions to fail when trying to access properties like `row.id` or `row.uuid`.
 *
 * This helper automatically detects loading states and provides safe fallback IDs.
 *
 * @param getRowId - The user's getRowId function.
 * @param isLoading - Whether the table is currently in a loading state.
 * @returns A safe getRowId function that handles loading states, or undefined if no getRowId provided.
 */
export function createSafeGetRowId<TData extends RowData>(
    getRowId: CoreOptions<TData>['getRowId'] | undefined,
    isLoading: boolean,
): CoreOptions<TData>['getRowId'] | undefined {
    if (!getRowId) {
        return undefined;
    }

    return (row: TData, index: number) => {
        // During loading, the row is just `0 as TData`, so we need to provide a fallback
        if (isLoading || row === (0 as TData)) {
            return `loading-row-${index}`;
        }

        try {
            return getRowId(row, index);
        } catch {
            return `fallback-row-${index}`;
        }
    };
}
