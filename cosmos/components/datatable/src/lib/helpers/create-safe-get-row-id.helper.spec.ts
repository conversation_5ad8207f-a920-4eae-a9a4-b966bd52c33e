import { describe, expect, test, vi } from 'vitest';
import { createSafeGetRowId } from './create-safe-get-row-id.helper';

interface TestData {
    id: number;
    name: string;
}

describe('createSafeGetRowId', () => {
    test('should return undefined when no getRowId is provided', () => {
        const result = createSafeGetRowId<TestData>(undefined, false);

        expect(result).toBeUndefined();
    });

    test('should return the original getRowId result when not loading', () => {
        const mockGetRowId = vi.fn((row: TestData) => `test-${row.id}`);
        const safeGetRowId = createSafeGetRowId(mockGetRowId, false);

        const testRow = { id: 123, name: 'Test' };
        const result = safeGetRowId?.(testRow, 0);

        expect(result).toBe('test-123');
        expect(mockGetRowId).toHaveBeenCalledWith(testRow, 0);
    });

    test('should return loading-row-{index} when isLoading is true', () => {
        const mockGetRowId = vi.fn((row: TestData) => `test-${row.id}`);
        const safeGetRowId = createSafeGetRowId(mockGetRowId, true);

        const testRow = { id: 123, name: 'Test' };
        const result = safeGetRowId?.(testRow, 5);

        expect(result).toBe('loading-row-5');
        expect(mockGetRowId).not.toHaveBeenCalled();
    });

    test('should return loading-row-{index} when row is fake loading data (0 as TData)', () => {
        const mockGetRowId = vi.fn((row: TestData) => `test-${row.id}`);
        const safeGetRowId = createSafeGetRowId(mockGetRowId, false);

        const fakeLoadingRow = 0 as unknown as TestData;
        const result = safeGetRowId?.(fakeLoadingRow, 3);

        expect(result).toBe('loading-row-3');
        expect(mockGetRowId).not.toHaveBeenCalled();
    });

    test('should return fallback-row-{index} when getRowId throws an error', () => {
        const mockGetRowId = vi.fn(() => {
            throw new Error('Property access failed');
        });
        const safeGetRowId = createSafeGetRowId(mockGetRowId, false);

        const testRow = { id: 123, name: 'Test' };
        const result = safeGetRowId?.(testRow, 2);

        expect(result).toBe('fallback-row-2');
        expect(mockGetRowId).toHaveBeenCalledWith(testRow, 2);
    });
});
