import { isEmpty } from 'lodash-es';
import { styled } from 'styled-components';
import { Stack } from '@cosmos/components/stack';
import {
    borderRadiusLg,
    borderWidthSm,
    dimension40x,
    neutralBackgroundSurfaceInitial,
    neutralBorderFaded,
} from '@cosmos/constants/tokens';
import { zIndex } from '@cosmos/constants/z-index';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { RowData, Table as TableType } from '@tanstack/react-table';
import type { DatatableTableMeta } from '../../types/datatable-table-meta.type';
import { BulkActions } from '../bulk-actions/bulk-actions';
import { EmptyStateTable } from '../empty-state-table/EmptyStateTable';
import { GalleryContainer } from '../gallery-container/gallery-container';
import { Pagination } from '../pagination/Pagination';
import { Table } from '../table/Table';
import { TopBar } from '../top-bar/TopBar';

export const StyledInnerTableContainerDiv = styled.div`
    display: flex;
    position: relative;
    flex-direction: column;
    border-radius: ${borderRadiusLg};
    min-height: 0;
    width: 100%;
    max-width: 100%;
    min-width: 0;


    background: ${neutralBackgroundSurfaceInitial};

    border-left: ${borderWidthSm} solid ${neutralBorderFaded};
    border-right: ${borderWidthSm} solid ${neutralBorderFaded};
`;

export interface TableContainerProps<TData extends RowData> {
    table: TableType<TData>;
}

export const TableContainer = <TData extends RowData>({
    table,
}: TableContainerProps<TData>): React.JSX.Element => {
    const {
        options: { data, meta },
    } = table;

    const {
        tableActions,
        filterProps,
        tableSearchProps,
        tableSettingsTriggerProps: tableSettingsAction,
    } = meta as DatatableTableMeta<TData>;

    // TODO use table.getViewState()
    const emptyStateProps = meta?.emptyStateProps;
    const isLoading = meta?.isLoading;
    const viewMode = meta?.viewMode;
    const hidePagination = meta?.hidePagination;
    const hasData = !isEmpty(data);
    const showEmptyState = emptyStateProps && !isLoading && !hasData;
    const showFilters = !isEmpty(filterProps);
    const showSearch = !tableSearchProps.hideSearch;
    const showTableActions = !isEmpty(tableActions);
    const showTableSettings = !isEmpty(tableSettingsAction);
    const showTopBar =
        showFilters || showSearch || showTableActions || showTableSettings;

    return (
        <StyledInnerTableContainerDiv
            data-testid="TableContainer"
            data-id="kU4IlWUF"
        >
            {showTopBar && <TopBar table={table} />}

            <Stack
                position="absolute"
                overflow="visible"
                justify="center"
                width="100%"
                height="0x"
                style={{
                    zIndex: zIndex.popover,
                    bottom: dimension40x,
                }}
            >
                <BulkActions table={table} />
            </Stack>

            {(() => {
                if (showEmptyState) {
                    return <EmptyStateTable table={table} />;
                }

                if (viewMode === 'table') {
                    return <Table table={table} />;
                }

                return <GalleryContainer table={table} data-id="lSV84RhX" />;
            })()}

            {!hidePagination && <Pagination table={table} />}
        </StyledInnerTableContainerDiv>
    );
};
