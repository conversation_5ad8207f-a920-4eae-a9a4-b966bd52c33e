import { isNil } from 'lodash-es';
import {
    use<PERSON>allback,
    useEffect,
    useMemo,
    useReducer,
    useRef,
    useState,
} from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import type { Filter } from '@cosmos/components/filter-field';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import {
    type CellContext,
    getCoreRowModel,
    isFunction,
    type RowData,
    type RowSelectionState,
    type SortingState,
    type Table,
    useReactTable,
} from '@tanstack/react-table';
import { EmptyStateTableCell } from '../components/empty-state-table-cell/empty-state-table-cell';
import { HeaderButton } from '../components/header-button/HeaderButton';
import { HeaderCheckbox } from '../components/header-checkbox/HeaderCheckbox';
import { RowButton } from '../components/row-button/RowButton';
import { RowCheckbox } from '../components/row-checkbox/RowCheckbox';
import { DEFAULT_COLUMN_MAX_SIZE } from '../constants/default-column-max-size.constant';
import { DEFAULT_COLUMN_MIN_SIZE } from '../constants/default-column-min-size.constant';
import { DEFAULT_PAGE } from '../constants/default-page.constant';
import { DEFAULT_PAGE_INDEX } from '../constants/default-page-index.constant';
import { DEFAULT_PAGE_SIZE } from '../constants/default-page-size.constant';
import { DEFAULT_PAGE_SIZE_OPTIONS } from '../constants/default-page-size-options.constant';
import { DEFAULT_TABLE_SEARCH_PROPS } from '../constants/default-table-search-props.constant';
import { DisplayStateFeature } from '../features/display-state.feature';
import { MetaFeature } from '../features/meta.feature';
import { createSafeGetRowId } from '../helpers/create-safe-get-row-id.helper';
import { isObserverComponent } from '../helpers/is-observer-component.helper';
import {
    type FilterAction,
    globalFilterStateReducer,
} from '../reducers/global-filter-state-reducer.reducer';
import type {
    DatatableProps,
    ExtendedDataTableColumnDef,
} from '../types/datatable-props.type';
import type { FetchDataResponseParams } from '../types/fetch-data-response-params.type';
import type { GlobalFilterState } from '../types/global-filter-state.type';

const selectionColumn = {
    id: 'select',
    header: HeaderCheckbox,
    cell: RowCheckbox,
    enableResizing: false,
    size: 'auto',
    minSize: 0,
    maxSize: 0,
    meta: {
        shouldIgnoreRowClick: true,
    },
};

const rowButtonColumn = {
    id: 'button',
    header: HeaderButton,
    cell: RowButton,
    enableResizing: false,
    size: 'auto',
    minSize: 0,
    maxSize: 0,
};

export const useDatatable = <TData extends RowData>({
    bulkActionDropdownItems = undefined,
    columns = [],
    data = [],
    defaultColumnOptions = {
        minSize: DEFAULT_COLUMN_MIN_SIZE,
        maxSize: DEFAULT_COLUMN_MAX_SIZE,
    },
    defaultPaginationOptions = {
        page: DEFAULT_PAGE,
        pageIndex: DEFAULT_PAGE_INDEX,
        pageSize: DEFAULT_PAGE_SIZE,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    },
    density = 'normal',
    emptyStateProps = undefined,
    filterProps = undefined,
    filterViewModeProps = { viewMode: 'toggleable' },
    getRowId,
    getSelectAllButtonText = undefined,
    hidePagination = false,
    cantDetermineTotalCount = false,
    initialSorting = [],
    isRowSelectionEnabled = undefined,
    isMultiRowSelectionEnabled = true,
    isLoading = false,
    isSortable = true,
    onFetchData = undefined,
    onRowClick = undefined,
    onRowSelection = undefined,
    tableActions = undefined,
    tableId,
    tableSearchProps = DEFAULT_TABLE_SEARCH_PROPS,
    tableSettingsTriggerProps = undefined,
    total,
    disabledRowSelectionCheckboxTooltip = undefined,
    viewMode = 'table',
    galleryCard,
    galleryCustomSkeletonCard,
    imperativeHandleRef,
    isFullPageTable,
}: DatatableProps<TData>): { table: Table<TData> } => {
    const initialFilters = useMemo(() => {
        return (
            // eslint-disable-next-line unicorn/no-array-reduce -- need this
            filterProps?.filters.reduce(
                (acc: GlobalFilterState['filters'], filter: Filter) => {
                    if ('value' in filter) {
                        acc[filter.id] = {
                            value: filter.value,
                            filterType: filter.filterType,
                        };
                    } else if ('defaultValue' in filter) {
                        acc[filter.id] = {
                            value: filter.defaultValue,

                            filterType: filter.filterType,
                        };
                    } else if ('defaultSelectedOptions' in filter) {
                        acc[filter.id] = {
                            value: filter.defaultSelectedOptions,
                            filterType: filter.filterType,
                        };
                    } else {
                        acc[filter.id] = {
                            value: undefined,
                            filterType: filter.filterType,
                        };
                    }

                    return acc;
                },
                {} as GlobalFilterState['filters'],
            ) ?? {}
        );
    }, [filterProps?.filters]);

    const [columnVisibility, setColumnVisibility] = useState({});
    const [isAllRowsSelected, setIsAllRowsSelected] = useState<boolean>(false);
    const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
    const [columnPinning, setColumnPinning] = useState({});
    const [sorting, setSorting] = useState<SortingState>(initialSorting);

    const [pagination, setPagination] = useState(defaultPaginationOptions);
    const [globalFilter, dispatchGlobalFilter] = useReducer(
        globalFilterStateReducer,
        {
            search: tableSearchProps.defaultValue,
            filters: initialFilters,
        },
    );

    const handleFetchData = useCallback(
        (params: FetchDataResponseParams) => {
            onFetchData?.({
                ...params,
                pagination: {
                    ...params.pagination,
                    page: params.pagination.pageIndex + 1,
                },
            });
        },
        [onFetchData],
    );

    const isMountingRowSelection = useRef(true);
    const isMountingFetchData = useRef(true);

    useEffect(() => {
        if (isMountingRowSelection.current) {
            isMountingRowSelection.current = false;

            return;
        }

        onRowSelection?.({ isAllRowsSelected, selectedRows: rowSelection });
    }, [onRowSelection, rowSelection, isAllRowsSelected]);

    useEffect(() => {
        if (isMountingFetchData.current) {
            isMountingFetchData.current = false;

            return;
        }
        handleFetchData({ globalFilter, pagination, sorting });
    }, [globalFilter, handleFetchData, pagination, sorting]);

    /**
     * NOTE: This does what table.firstPage() does
     * We need it here because we don't have access to the table object yet
     * and we use it only when filters are changed.
     */
    const goToFirstPage = () => {
        setPagination((prevPagination) => {
            return {
                ...prevPagination,
                pageIndex: 0,
            };
        });
    };

    const resetRowSelection = () => {
        setRowSelection({});
        setIsAllRowsSelected(false);
    };

    const handleGlobalFilterChange = useCallback(
        (action: FilterAction) => {
            /**
             * NOTE: React 17 does not auto batch calls to update the state
             * Without batching we are triggering "handleFetchData" before we're ready
             * 1. Pagination changes, call handleFetchData
             * 2. Row selection changes, call handleFetchData
             * 3. Global filter changes, call handleFetchData.
             *
             * Its possible that this can be resolved without "unstable_batchedUpdates" by
             * making GlobalFilters, a legit tanstack table feature, which will give it direct access to table state
             * instead of needing to rely on multiple separate pieces of our own custom state.
             *
             * Https://tanstack.com/table/latest/docs/guide/custom-features.
             */
            unstable_batchedUpdates(() => {
                switch (action.type) {
                    case 'SET_SEARCH': {
                        goToFirstPage();
                        if (isAllRowsSelected) {
                            resetRowSelection();
                        }
                        dispatchGlobalFilter({
                            type: 'SET_SEARCH',
                            payload: action.payload,
                        });
                        break;
                    }
                    case 'RESET_FILTERS': {
                        goToFirstPage();
                        if (isAllRowsSelected) {
                            resetRowSelection();
                        }
                        if (filterProps?.onClearFiltersFn) {
                            filterProps.onClearFiltersFn();
                        }
                        dispatchGlobalFilter({
                            type: 'RESET_FILTERS',
                            payload: initialFilters,
                        });
                        break;
                    }
                    case 'UPDATE_FILTERS': {
                        goToFirstPage();
                        if (isAllRowsSelected) {
                            resetRowSelection();
                        }
                        dispatchGlobalFilter({
                            type: 'UPDATE_FILTERS',
                            payload: action.payload,
                        });
                        break;
                    }
                    default: {
                        throw new Error('Invalid action type');
                    }
                }
            });
        },
        [initialFilters, isAllRowsSelected, filterProps],
    );

    const memoizedColumns: ExtendedDataTableColumnDef<TData>[] = useMemo(() => {
        return [
            isRowSelectionEnabled && selectionColumn,
            ...columns.map((column) => ({
                ...column,
                cell: (props: CellContext<TData, unknown>) => {
                    const value = props.getValue();

                    if (
                        (isNil(value) || value === '') &&
                        !column.isActionColumn
                    ) {
                        return EmptyStateTableCell();
                    }
                    if (isFunction(column.cell)) {
                        return (
                            column.cell as (
                                context: CellContext<TData, unknown>,
                            ) => React.ReactNode
                        )(props);
                    }

                    if (isObserverComponent(column.cell)) {
                        return column.cell.type(props);
                    }

                    return value;
                },
            })),
            onRowClick && rowButtonColumn,
        ].filter(Boolean) as DatatableProps<TData>['columns'];
    }, [isRowSelectionEnabled, columns, onRowClick]);

    // TO-THINK: Maybe a hook to standardize and encapsulate memoization of table configs
    const tableSearchPropsWithDefaults = useMemo(() => {
        return {
            ...DEFAULT_TABLE_SEARCH_PROPS,
            ...tableSearchProps,
        };
    }, [tableSearchProps]);

    const dataToUse = useMemo(
        () =>
            isLoading
                ? Array<TData>(pagination.pageSize).fill(0 as TData)
                : data,
        [isLoading, pagination.pageSize, data],
    );

    const setPageIndex = useCallback(
        (pageIndex: number) => {
            let page = pageIndex;

            if (Number.isNaN(page)) {
                return;
            }
            if (page < 0) {
                page = 0;
            }
            const lastPage = Math.ceil(total / pagination.pageSize) - 1;

            if (lastPage > 0 && page > lastPage) {
                page = lastPage;
            }
            setPagination((prev) => ({ ...prev, pageIndex: page }));
        },
        [total, pagination.pageSize],
    );

    // useImperativeHandle alternative to expose setPageIndex to parent component
    useEffect(() => {
        if (imperativeHandleRef) {
            Object.assign(imperativeHandleRef, {
                current: { setPageIndex, resetRowSelection },
            });
        }

        return () => {
            if (imperativeHandleRef) {
                Object.assign(imperativeHandleRef, { current: null });
            }
        };
    }, [imperativeHandleRef, setPageIndex]);

    const table = useReactTable({
        /**
         * Meta will be used as context for internal components
         * If you need to pass something down to a nested child component,
         * add it here and update the TableMeta interface at libs/datatable/src/\@types/tanstack-table.d.ts.
         */
        meta: {
            emptyStateProps,
            filterProps,
            filterViewModeProps,
            hidePagination,
            isLoading,
            tableId,
            tableSearchProps: tableSearchPropsWithDefaults,
            tableSettingsTriggerProps,
            bulkActionDropdownItems,
            isRowSelectionEnabled,
            isMultiRowSelectionEnabled,
            tableActions,
            setIsAllRowsSelected,
            onRowClick,
            getSelectAllButtonText,
            disabledRowSelectionCheckboxTooltip,
            viewMode,
            galleryCard,
            galleryCustomSkeletonCard,
            density,
            cantDetermineTotalCount,
            isFullPageTable,
            /*
             * The `select all across all pages` functionality is intentionally disabled
// eslint-disable-next-line lodash-f/prefer-lodash-typecheck -- need this
             * in scenarios where the `isRowSelectionEnabled` prop is used to determine
             * if individual rows are selectable. Here's why:
             *
             * 1. `isRowSelectionEnabled` can either be:
             * - A boolean: In this case, row selection is consistently enabled or disabled for all rows.
             * - A function: When it's a function, it evaluates each row individually and determines
             * whether that specific row can be selected.
             *
             * 2. In server-side paginated data, we only have visibility into the rows currently
             * loaded in the current page. For rows on other pages:
             * - The `isRowSelectionEnabled` function cannot evaluate their state because they
             * are not loaded in memory.
             * - This makes it impossible to accurately determine if all rows across all pages
             * should be selectable.
             *
             * 3. To avoid inconsistent behavior or incorrect assumptions about row states
             * on other pages, the `select all across all pages` functionality is hidden
             * when `isRowSelectionEnabled` is a function.
             */
            isSelectAllButtonHidden:
                // eslint-disable-next-line lodash-f/prefer-lodash-typecheck -- need this
                typeof isRowSelectionEnabled === 'function',
        },
        enableRowSelection: isRowSelectionEnabled,
        enableMultiRowSelection: isMultiRowSelectionEnabled,
        data: dataToUse,
        columns: memoizedColumns,
        defaultColumn: defaultColumnOptions,
        getCoreRowModel: getCoreRowModel(),

        /**
         * START server side sorting.
         */
        manualSorting: true,
        onSortingChange: setSorting,
        enableSorting: isSortable,
        /**
         * END server side sorting.
         */

        /**
         * START Server side pagination.
         */
        manualPagination: true,
        onPaginationChange: setPagination,
        rowCount: total,
        /**
         * END Server side pagination.
         */

        /**
         * START Server side filtering.
         */
        onGlobalFilterChange: handleGlobalFilterChange,
        /**
         * END Server side filtering.
         */

        enableColumnResizing: true,
        columnResizeMode: 'onChange',
        onColumnVisibilityChange: setColumnVisibility,
        onColumnPinningChange: setColumnPinning,

        /**
         * START Row Selection.
         */
        onRowSelectionChange: setRowSelection,
        getRowId: createSafeGetRowId(getRowId, isLoading),
        /**
         * END Row Selection.
         */

        initialState: {
            globalFilter: {
                search: tableSearchProps.defaultValue,
                filters: initialFilters,
            },
        },
        state: {
            isAllRowsSelected,
            columnPinning,
            columnVisibility,
            globalFilter,
            pagination,
            rowSelection,
            sorting,
        },
        _features: [MetaFeature, DisplayStateFeature],
    });

    return { table };
};
