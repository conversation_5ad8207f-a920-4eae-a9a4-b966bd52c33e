import type { CSSProperties } from 'react';
import { css, styled } from 'styled-components';
import {
    borderRadius2x,
    borderWidthSm,
    dimensionMd,
    dimensionSm,
    dimensionXs,
    fontFamilyDefault,
    fontSize200,
    fontWeightRegular,
    lineHeight200,
    neutralBackgroundMild,
    neutralBorderHover,
    neutralBorderInitial,
    neutralTextInitial,
    primaryBorderFocus,
    primaryBorderInitial,
} from '@cosmos/constants/tokens';

export const StyledWrapperDiv = styled.div`
    display: flex;
    flex-direction: column;
    gap: ${dimensionSm};
`;

const readOnlyStyles = css`
    background-color: ${neutralBackgroundMild};
    border-color: ${neutralBorderInitial};
    &:hover,
    &:focus-visible {
        background-color: ${neutralBackgroundMild};
        border-color: ${neutralBorderInitial};
        outline: none;
    }
`;

export const StyledTextarea = styled.textarea<{
    $feedbackBorderColor?: CSSProperties['borderColor'];
    $gridArea?: string;
    $height?: number;

    $isReadOnly?: boolean;
}>`
    grid-area: ${({ $gridArea }) => $gridArea && `grid-area: ${$gridArea}`};

    box-sizing: border-box;
    border: ${borderWidthSm} solid ${neutralBorderInitial};
    border-color: ${({ $feedbackBorderColor }) => $feedbackBorderColor};
    border-radius: ${borderRadius2x};
    color: ${neutralTextInitial};
    font-family: ${fontFamilyDefault};
    font-size: ${fontSize200};
    font-weight: ${fontWeightRegular};
    line-height: ${lineHeight200};
    width: 100%;
    resize: vertical;
    padding: ${dimensionMd};
    &:hover {
        border-color: ${neutralBorderHover};
    }

    &:focus-visible {
        border-color: ${primaryBorderInitial};
        outline: ${dimensionXs} solid ${primaryBorderFocus};
        outline-offset: ${dimensionXs};
    }

    &:active {
        border: ${borderWidthSm} solid ${primaryBorderInitial};
    }

    ${({ $isReadOnly }) => $isReadOnly && readOnlyStyles}
`;
