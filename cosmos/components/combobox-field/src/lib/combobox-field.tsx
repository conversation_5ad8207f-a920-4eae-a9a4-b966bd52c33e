import { Combobox, type ComboboxProps } from '@cosmos/components/combobox';
import { FormField, type FormFieldProps } from '@cosmos/components/form-field';
import { DEFAULT_DATA_ID } from './constants';

export interface ComboboxFieldProps
    extends Omit<ComboboxProps, 'id' | 'aria-labelledby' | 'aria-describedby'>,
        Pick<
            FormFieldProps,
            | 'label'
            | 'labelStyleOverrides'
            | 'helpText'
            | 'shouldHideLabel'
            | 'optionalText'
            | 'feedback'
            | 'formId'
            | 'name'
            | 'required'
        > {
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Text that appears in the form control when it has no value set.
     */
    placeholder?: string;
}

/**
 * A form field which allows users to select one or many options from a list.  The list may be loaded synchronously or asynchronously.
 *
 * [ComboboxField in Figma](https://www.figma.com/file/E98sepyU91vSTn4VeWnzmt/Cosmos-Components?type=design&node-id=14368%3A48935&mode=design&t=N8x2h5Qj8bRkcazK-1).
 */
export const ComboboxField = ({
    'data-id': dataId = DEFAULT_DATA_ID,
    defaultSelectedOptions = undefined,
    defaultValue = undefined,
    disabled = false,
    feedback = undefined,
    formId,
    getRemoveIndividualSelectedItemClickLabel,
    getSearchEmptyState,
    helpText = undefined,
    shouldHideLabel = false,
    imperativeHandleRef = undefined,
    isMultiSelect = undefined,
    itemToString = undefined,
    label,
    labelStyleOverrides = undefined,
    loaderLabel,
    name,
    onBlur = undefined,
    onChange,
    onFocus = undefined,
    onKeyDown = undefined,
    optionalText = undefined,
    options = undefined,
    placeholder = undefined,
    removeAllSelectedItemsLabel,
    required = false,
    tagGroupColorScheme = undefined,
    readOnly = false,
    clearSelectedItemButtonLabel = undefined,
    hasMore = false,
    onFetchOptions = undefined,
    isLoading = false,
    enableFetchOnFocus,
    disableFetchOnMount,
}: ComboboxFieldProps): React.JSX.Element => {
    return (
        <FormField
            data-id={dataId}
            label={label}
            labelStyleOverrides={labelStyleOverrides}
            helpText={helpText}
            shouldHideLabel={shouldHideLabel}
            optionalText={optionalText}
            feedback={feedback}
            formId={formId}
            name={name}
            required={required}
            data-testid="ComboboxField"
            renderInput={({
                describeIds,
                gridArea,
                inputId,
                inputTestId,
                labelId,
                feedbackType,
            }) => {
                return (
                    <Combobox
                        aria-describedby={describeIds}
                        aria-labelledby={labelId}
                        data-id={inputTestId}
                        defaultSelectedOptions={defaultSelectedOptions}
                        defaultValue={defaultValue}
                        disabled={disabled}
                        feedbackType={feedbackType}
                        getSearchEmptyState={getSearchEmptyState}
                        gridArea={gridArea}
                        id={inputId}
                        imperativeHandleRef={imperativeHandleRef}
                        isMultiSelect={isMultiSelect}
                        itemToString={itemToString}
                        loaderLabel={loaderLabel}
                        name={name}
                        options={options}
                        hasMore={hasMore}
                        enableFetchOnFocus={enableFetchOnFocus}
                        disableFetchOnMount={disableFetchOnMount}
                        placeholderText={placeholder}
                        required={required}
                        tagGroupColorScheme={tagGroupColorScheme}
                        readOnly={readOnly}
                        isLoading={isLoading}
                        getRemoveIndividualSelectedItemClickLabel={
                            getRemoveIndividualSelectedItemClickLabel
                        }
                        removeAllSelectedItemsLabel={
                            removeAllSelectedItemsLabel
                        }
                        clearSelectedItemButtonLabel={
                            clearSelectedItemButtonLabel
                        }
                        onBlur={onBlur}
                        onChange={onChange}
                        onFocus={onFocus}
                        onKeyDown={onKeyDown}
                        onFetchOptions={onFetchOptions}
                    />
                );
            }}
        />
    );
};
