import { FEEDBACK_TYPE_OPTIONS } from '@cosmos/components/field-feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { Meta } from '@storybook/react-vite';
import { Combobox } from '../combobox';
import { SAMPLE_ITEMS } from './constants';

const meta: Meta<typeof Combobox> = {
    tags: ['Private'],
    title: 'Forms/Combobox',
    component: Combobox,
    argTypes: {
        feedbackType: {
            control: 'radio',
            options: [...FEEDBACK_TYPE_OPTIONS, undefined],
        },
        tagGroupColorScheme: {
            control: 'select',
            options: [
                'primary',
                'neutral',
                'critical',
                'warning',
                'success',
                'education',
            ],
        },
        onBlur: {
            control: false,
            action: 'onBlur',
        },
        onChange: {
            control: false,
            action: 'onChange',
        },
        onFocus: {
            control: false,
            action: 'onFocus',
        },
        onKeyDown: {
            control: false,
            action: 'onKeyDown',
        },
        onFilterOptions: {
            control: false,
            action: 'onFilterOptions',
        },
        itemToString: {
            control: false,
            action: 'itemToString',
        },
    },
    args: {
        'aria-describedby': 'helpText-id',
        'aria-labelledby': 'label-id',
        'data-id': 'selectTestId',
        defaultValue: undefined,
        disabled: false,
        feedbackType: undefined,
        getRemoveIndividualSelectedItemClickLabel: ({ itemLabel }) =>
            `Remove ${itemLabel}`,
        gridArea: 'input',
        id: 'playgroundSelect',
        isMultiSelect: false,
        itemToString: undefined,
        loaderLabel: 'Loading results',
        name: 'cosmos-combobox',
        onBlur: undefined,
        onChange: (value: ListBoxItemData | undefined) => {
            // eslint-disable-next-line no-console -- need this
            console.log('onChange', value);
        },
        onFilterOptions: undefined,
        onFocus: undefined,
        onKeyDown: undefined,
        options: SAMPLE_ITEMS,
        placeholderText: 'Select one option',
        removeAllSelectedItemsLabel: 'Remove All',
        required: false,
        searchDebounce: undefined,
        tagGroupColorScheme: 'primary',
        clearSelectedItemButtonLabel: 'Clear',
        disableFetchOnMount: false,
        enableFetchOnFocus: false,
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page

export { Playground } from './stories';
