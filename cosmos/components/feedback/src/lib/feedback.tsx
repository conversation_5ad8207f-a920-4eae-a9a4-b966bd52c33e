/* eslint-disable @eslint-react/no-leaked-conditional-rendering -- need this */
import type { ReactNode } from 'react';
import { Icon } from '@cosmos/components/icon';
import { Text } from '@cosmos/components/text';
import { DEFAULT_DATA_ID, SEVERITIES } from './constants';
import {
    StyledStackDiv,
    StyledTextWrapperSpan,
    StyledWrapperDiv,
} from './styles';
import type { Severity } from './types';

export interface FeedbackProps {
    /**
     * The content to be displayed below.
     */
    description?: ReactNode;
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * The primary message of the Feedback, displayed at the top.
     */
    title: ReactNode;
    /**
     * The displayed sentiment of the Feedback; controls the colors and icon.
     */
    severity?: Severity;
}

/**
 * The Feedback component provides contextual feedback to the user based on their actions or current status of a line item within Drata. This could include success messages after a task completion, warnings for potential errors, or information messages to guide users in a concise context.
 *
 * [Feedback in Figma](https://www.figma.com/file/E98sepyU91vSTn4VeWnzmt/Cosmos-Components?type=design&node-id=10206-36509&mode=design&t=ozkq6TKAqw84Qjwt-0).
 */
export const Feedback = ({
    description = '',
    'data-id': dataId = DEFAULT_DATA_ID,
    title,
    severity = 'primary',
}: FeedbackProps): React.JSX.Element => {
    const { color, iconName } = SEVERITIES[severity];

    return (
        <StyledWrapperDiv
            $color={color}
            data-id={dataId}
            data-testid="Feedback"
        >
            <Icon
                name={iconName}
                colorScheme="inherit"
                size="100"
                data-id={`${dataId}-icon`}
            />

            <StyledStackDiv>
                <StyledTextWrapperSpan>
                    <Text
                        colorScheme="inherit"
                        size="100"
                        type="title"
                        data-id={`${dataId}-title`}
                    >
                        {title}
                    </Text>
                </StyledTextWrapperSpan>
                {description && (
                    <StyledTextWrapperSpan>
                        <Text
                            colorScheme="inherit"
                            size="100"
                            type="body"
                            data-id={`${dataId}-body`}
                        >
                            {description}
                        </Text>
                    </StyledTextWrapperSpan>
                )}
            </StyledStackDiv>
        </StyledWrapperDiv>
    );
};
