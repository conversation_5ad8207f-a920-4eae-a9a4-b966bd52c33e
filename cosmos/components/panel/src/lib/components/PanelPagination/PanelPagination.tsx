import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import { styled } from 'styled-components';
import { Button } from '@cosmos/components/button';
import { Text } from '@cosmos/components/text';
import { dimensionMd } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import type { BasePanelProps } from '../../types';

const StyledContentDiv = styled.div`
    display: flex;
    align-items: center;
    gap: ${dimensionMd};
`;

export type PanelPaginationProps = BasePanelProps & {
    currentItem: number;
    totalItems: number;
    onPrevPageClick: MouseEventHandler<HTMLElement>;
    onNextPageClick: MouseEventHandler<HTMLElement>;
};

export const PanelPagination = ({
    currentItem,
    totalItems,
    onPrevPageClick,
    onNextPageClick,

    'data-id': dataId = 'panel-pagination',
}: PanelPaginationProps): React.JSX.Element => {
    const showPaginationStatus = currentItem <= totalItems;

    return (
        <StyledContentDiv data-id={dataId} data-testid="PanelPagination">
            {showPaginationStatus && (
                <Text
                    data-id={`${dataId}-status-text`}
                    type="headline"
                    size="100"
                    colorScheme="faded"
                >
                    {currentItem} of {totalItems}
                </Text>
            )}
            <Button
                level="secondary"
                colorScheme="neutral"
                size="sm"
                endIconName="ArrowUp"
                label={t`Previous`}
                data-id={`${dataId}-previous-page-button`}
                onClick={onPrevPageClick}
            />
            <Button
                level="secondary"
                colorScheme="neutral"
                size="sm"
                endIconName="ArrowDown"
                label={t`Next`}
                data-id={`${dataId}-next-page-button`}
                onClick={onNextPageClick}
            />
        </StyledContentDiv>
    );
};
