import { type ReactNode, useState } from 'react';
import { styled } from 'styled-components';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
// eslint-disable-next-line no-restricted-imports -- Legacy usage DO NOT COPY Fix later
import { Link, type LinkProps } from '@cosmos/components/link';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import {
    borderWidthSm,
    dimension2xl,
    dimensionMd,
    neutralBackgroundMild,
    neutralBorderFaded,
} from '@cosmos/constants/tokens';
import { Truncation } from '@cosmos-lab/components/truncation';
import { t } from '@globals/i18n/macro';
import { panelPaddingMixin } from '../../styles';
import type { BasePanelProps } from '../../types';

const StyledContentDiv = styled.div`
    display: flex;
    flex-direction: column;
    gap: ${dimensionMd};

    background-color: ${neutralBackgroundMild};
    border-bottom: ${borderWidthSm} solid ${neutralBorderFaded};

    ${panelPaddingMixin}
`;

const StyledLinkContainerDiv = styled.div`
    display: flex;
    gap: ${dimension2xl};
`;

export type PanelHeaderProps = BasePanelProps & {
    /**
     * The main title for the panel, providing a concise description or label for the panel content.
     */
    title: string;

    /**
     * Flexible slot section of the header's content.
     */
    slot?: ReactNode;

    /**
     * Links to related resources.
     */
    relatedPageLinks?: LinkProps[];

    /**
     * Number of lines to display before truncating the title.
     * Default 2.
     */
    lineClamp?: number;

    /**
     * Link for redirect when the open page link is clicked.
     */
    openPageLink?: string;
};

export const PanelHeader = ({
    title,
    slot = undefined,
    relatedPageLinks = undefined,
    lineClamp = 2,
    openPageLink = undefined,
    'data-id': dataId = 'panel-header',
}: PanelHeaderProps): React.JSX.Element => {
    const [isTitleTruncated, setIsTitleTruncated] = useState(false);

    return (
        <StyledContentDiv data-id={dataId} data-testid="PanelHeader">
            <Grid gap="md" columns="1fr auto">
                {slot}
                {openPageLink && (
                    <Button
                        data-id={`${dataId}-open-link`}
                        as="a"
                        href={openPageLink}
                        label={t`Open`}
                        level="secondary"
                        size="sm"
                        colorScheme="primary"
                        endIconName="Expand"
                    />
                )}
            </Grid>
            <Tooltip text={title} isDisabled={!isTitleTruncated}>
                <Text
                    data-id={`${dataId}-title`}
                    type="headline"
                    size="500"
                    colorScheme="neutral"
                >
                    <Truncation
                        mode="end"
                        lineClamp={lineClamp}
                        onTruncate={setIsTitleTruncated}
                    >
                        {title}
                    </Truncation>
                </Text>
            </Tooltip>
            {relatedPageLinks && (
                <StyledLinkContainerDiv>
                    {relatedPageLinks.map((pageLink) => {
                        return (
                            <Link
                                {...pageLink}
                                key={pageLink['data-id']}
                                data-id={`${dataId}-related-page-link`}
                            />
                        );
                    })}
                </StyledLinkContainerDiv>
            )}
        </StyledContentDiv>
    );
};
