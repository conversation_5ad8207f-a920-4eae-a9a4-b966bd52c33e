import { isEmpty, isNil } from 'lodash-es';
import { sharedAuthController } from '@controllers/auth';
import {
    auditorsControllerGetMyUserOptions,
    usersControllerGetMyUserOptions,
} from '@globals/api-sdk/queries';
import type {
    AuditorMeResponseDto,
    MeResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import type { RBACAction } from './rbac-actions.type';
import type { RBACSubject } from './rbac-subject.type';

interface UserPermission {
    subject: string;
    action: string;
    [key: string]: unknown;
}

class CurrentUserController {
    userQuery = new ObservedQuery(usersControllerGetMyUserOptions);
    auditorGetMyUserQuery = new ObservedQuery(
        auditorsControllerGetMyUserOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    load() {
        if (this.hasAuditorToken) {
            this.auditorGetMyUserQuery.load();
        } else {
            this.userQuery.load();
        }
    }

    unload(): void {
        if (this.hasAuditorToken) {
            this.auditorGetMyUserQuery.unload();
        } else {
            this.userQuery.unload();
        }
    }

    get user(): MeResponseDto | AuditorMeResponseDto | null {
        return this.hasAuditorToken
            ? this.auditorGetMyUserQuery.data
            : this.userQuery.data;
    }

    /**
     * Type guard to check if the user is a regular user (not an auditor)
     * Regular users have permissions, auditors do not.
     */
    isRegularUser(
        _user: MeResponseDto | AuditorMeResponseDto | null,
    ): _user is MeResponseDto {
        return !this.hasAuditorToken;
    }

    get entryId(): string {
        return this.user?.entryId ?? '';
    }

    get fullName(): string {
        return getFullName(this.user?.firstName, this.user?.lastName);
    }

    get initials(): string {
        return getInitials(this.fullName);
    }

    get avatarUrl(): string | null {
        return this.user?.avatarUrl ?? null;
    }

    get isLoading() {
        return this.hasAuditorToken
            ? this.auditorGetMyUserQuery.isLoading
            : this.userQuery.isLoading;
    }

    get isUserInReadOnlyMode(): boolean {
        // TODO: @amacsim please check here
        // return !!getSessionItem(ACT_AS_READ_ONLY_SESSION_KEY);
        return false;
    }

    hasUserPermission(subject: RBACSubject, action: RBACAction): boolean {
        /**
         * Stops support users (read-only) from making a POST request on their initial login.
         * This action will trigger a 'not authorized' error, preventing tenant access.
         */
        if (this.isUserInReadOnlyMode && action === 'MANAGE') {
            return false;
        }

        /* Currently Auditors don't have user's permissions.
        We added this short circuit to allow use the READ permissions.
        We will remove this validation when the Auditors have permissions */
        if (
            (this.isAuditorInReadOnlyMode && action === 'READ') ||
            !this.isRegularUser(this.user)
        ) {
            return true;
        }

        // For regular users, check their permissions
        const { permissions = [] } = this.user;

        if (isEmpty(permissions)) {
            return false;
        }

        const foundPermission = permissions.find((permission) => {
            const typedPermission = permission as UserPermission;

            return (
                typedPermission.subject === subject &&
                typedPermission.action === action
            );
        });

        return !isNil(foundPermission);
    }

    /**
     * Check if it is admin.
     *
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isAdmin(): boolean {
        return this.roles.includes('ADMIN');
    }

    /**
     * Checks if the current user has the EMPLOYEE role.
     *
     * @returns True if user has EMPLOYEE role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isEmployee(): boolean {
        return this.roles.includes('EMPLOYEE');
    }

    /**
     * Checks if the current user has the TECHGOV role.
     *
     * @returns True if user has TECHGOV role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isTechgov(): boolean {
        return this.roles.includes('TECHGOV');
    }

    /**
     * Checks if the current user has the RISK_MANAGER role.
     *
     * @returns True if user has RISK_MANAGER role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isRiskManager(): boolean {
        return this.roles.includes('RISK_MANAGER');
    }

    get isRiskManagerWithRestrictedView(): boolean {
        return (
            this.roles.includes('RISK_MANAGER') &&
            !this.hasUserPermission('ViewAllRisks', 'READ')
        );
    }

    /**
     * Checks if the current user has the CONTROL_MANAGER role.
     *
     * @returns True if user has CONTROL_MANAGER role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isControlManager(): boolean {
        return this.roles.includes('CONTROL_MANAGER');
    }

    /**
     * Checks if the current user has the PEOPLE_OPS role.
     *
     * @returns True if user has PEOPLE_OPS role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isPeopleOps(): boolean {
        return this.roles.includes('PEOPLE_OPS');
    }

    /**
     * Checks if the current user has the POLICY_MANAGER role.
     *
     * @returns True if user has POLICY_MANAGER role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isPolicyManager(): boolean {
        return this.roles.includes('POLICY_MANAGER');
    }

    /**
     * Checks if the current user has the REVIEWER role.
     *
     * @returns True if user has REVIEWER role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isReviewer(): boolean {
        return this.roles.includes('REVIEWER');
    }

    /**
     * Checks if the current user has the SERVICE_USER role.
     *
     * @returns True if user has SERVICE_USER role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isServiceUser(): boolean {
        return this.roles.includes('SERVICE_USER');
    }

    /**
     * Checks if the current user has the AUDITOR role and is not a SERVICE_USER.
     *
     * @returns True if user has AUDITOR role and not SERVICE_USER role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isAuditor(): boolean {
        const hasAuditorRole = this.roles.includes('AUDITOR');

        return hasAuditorRole && !this.isServiceUser;
    }

    /**
     * Checks if the current user has the ACT_AS_READ_ONLY role.
     *
     * @returns True if user has ACT_AS_READ_ONLY role.
     * @deprecated If you really need it, go for it. Otherwise use hasUserPermission with proper Subject and action.
     */
    get isActAsReadOnly(): boolean {
        return this.roles.includes('ACT_AS_READ_ONLY');
    }

    get roles(): string[] {
        return this.user?.roles ?? [];
    }

    get hasAcceptedTerms(): boolean {
        return Boolean(this.user?.drataTermsAgreedAt);
    }

    get isAuditorInReadOnlyMode(): boolean {
        // TODO: @amacsim please check here
        // const isAuditorReadOnlyFrameEnabled = () => {
        //     return !!appCore.storage.getSessionStorageItem(
        //         AUDITOR_READ_ONLY_SESSION_KEY,
        //     );
        // };
        return false;
    }

    get homePagePathname(): string {
        return this.hasAuditorToken
            ? '/audit-hub/clients'
            : '/workspaces/1/quick-start';
    }

    get hasAuditorToken(): boolean {
        return sharedAuthController.authMode === 'AUDITOR';
    }
}

export const sharedCurrentUserController = new CurrentUserController();
